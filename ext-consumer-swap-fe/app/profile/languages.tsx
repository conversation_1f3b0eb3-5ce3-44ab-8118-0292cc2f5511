import { CloseButton } from "@/components/CloseButton";
import { FontStyles } from "@/constants/FontStyles";
import { getColor } from "@/utils/style";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Check, Languages } from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const styles = StyleSheet.create({
  safeArea: { flex: 1, width: "100%", backgroundColor: getColor("background") },

  container: {
    flex: 1,
    flexDirection: "column",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    paddingHorizontal: 16,
    gap: 32,
    paddingBottom: 32,
  },
});

const LinkRow = ({
  title,
  onPress,
  selected,
}: {
  icon?: React.ReactNode;
  title: string;
  onPress?: () => void;
  selected: boolean;
}) => {
  const color = selected ? getColor("primary") : getColor("foreground");
  return (
    <TouchableOpacity onPress={onPress}>
      <View
        style={{
          flexDirection: "row",
          gap: 8,
          paddingVertical: 8,
          justifyContent: "flex-start",
          alignItems: "center",
          flex: 1,
        }}
      >
        <View style={{ flexDirection: "column", gap: 4, flex: 1 }}>
          <View
            style={{
              flexDirection: "row",
              gap: 0,
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={[FontStyles.heading3, { color }]}>{title}</Text>
            {selected ? <Check color={color} size={16} /> : null}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const AccountSettings = () => {
  const { i18n } = useTranslation();
  const { t } = useTranslation();

  const languagesMap = [
    {
      key: "id",
      name: "Bahasa Indonesia",
      selected: i18n.language === "id",
    },
    {
      key: "en",
      name: "English",
      selected: i18n.language === "en",
    },
  ];

  const setAndStoreLanguageLocale = async (locale: string) => {
    try {
      i18n.changeLanguage(locale);
      await AsyncStorage.setItem("language", locale);
    } catch (e) {
      // default on error
      i18n.changeLanguage("en");
      await AsyncStorage.setItem("language", "en");
    }
  };

  return (
    <View style={{ gap: 16, flex: 1, width: "100%" }}>
      <Text style={FontStyles.heading2}>{t("uiPrompts.languages")}</Text>
      {languagesMap.map((language) => (
        <LinkRow
          key={language.key}
          icon={<Languages size={24} color={getColor("primary")} />}
          title={language.name}
          selected={language.selected}
          onPress={() => setAndStoreLanguageLocale(language.key)}
        />
      ))}
    </View>
  );
};

export default function Profile() {
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView>
        <View style={styles.container}>
          <CloseButton
            style={{
              width: "100%",
              flexDirection: "row-reverse",
              justifyContent: "flex-start",
            }}
          />
          <AccountSettings />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
