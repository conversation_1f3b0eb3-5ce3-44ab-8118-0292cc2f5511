import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { getColor } from "@/utils/style";
import { CameraRef } from "@rnmapbox/maps/lib/typescript/src/components/Camera";
import { LocateFixed } from "lucide-react-native";

import { RefObject, useCallback } from "react";
import { Pressable } from "react-native";

interface UseResetLocationI {
  cameraRef: RefObject<CameraRef>;
  top: number;
}

export default function useResetLocation({ cameraRef, top }: UseResetLocationI) {
  const { location } = useUserLocationContext();

  const resetCameraLocation = useCallback(() => {
    if (location && cameraRef.current) {
      cameraRef.current.moveTo([location.longitude, location.latitude], 200);
    }
  }, [location, cameraRef]);

  return {
    ResetLocationButton: () => (
      <Pressable
        onPress={resetCameraLocation}
        style={{
          position: "absolute",
          top: top + 16,
          left: 16,
          backgroundColor: getColor("background"),
          width: 40,
          height: 40,
          borderRadius: 20,
          justifyContent: "center",
          alignItems: "center",
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.15,
          shadowRadius: 4,
          elevation: 5,
        }}
      >
        <LocateFixed color={getColor("foreground")} size={24} />
      </Pressable>
    ),
  };
}
