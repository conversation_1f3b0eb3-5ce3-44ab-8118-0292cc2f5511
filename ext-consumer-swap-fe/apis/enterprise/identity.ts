const baseUrl = "https://consumerswap.api.dev.id.aerovy.com";

export const getIdentity = async (token: string, userId: string) => {
  const response = await fetch(`${baseUrl}/v1/identity/user/${userId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  if (!response.ok) {
    throw new Error("Failed to get identity");
  }
  return response.json();
};

type UpdatableIdentityFields = Partial<{
  firstName: string;
  lastName: string;
  profilePicture: string;
}>;

export const updateIdentity = async (token: string, userId: string, data: UpdatableIdentityFields) => {
  const response = await fetch(`${baseUrl}/v1/identity/user/${userId}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to update identity");
  }
  return response.json();
};

export const deleteIdentity = async (token: string, userId: string) => {
  const response = await fetch(`${baseUrl}/v1/identity/user/${userId}`, {
    method: "DELETE",
  });
  if (!response.ok) {
    throw new Error("Failed to delete identity");
  }
  return response.json();
};

export const updateIdentityEmail = async (token: string, userId: string, email: string) => {
  const response = await fetch(`${baseUrl}/v1/identity/user/${userId}/email`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ email }),
  });
  if (!response.ok) {
    throw new Error("Failed to update identity email");
  }
  return response.json();
};

export const resetIdentityPassword = async (token: string, userId: string) => {
  const response = await fetch(`${baseUrl}/v1/identity/user/${userId}/password`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  if (!response.ok) {
    throw new Error("Failed to reset identity password");
  }
  return response.json();
};
