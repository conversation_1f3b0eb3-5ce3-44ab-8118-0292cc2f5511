import { Button, ButtonVariant } from "@/components/Button";

import { Image, View } from "react-native";
import { useAuth0 } from "react-native-auth0";

import { Map } from "./map";

const SignupButton = () => {
  const { authorize } = useAuth0();

  const onPress = async () => {
    try {
      await authorize({
        scope: "openid profile email",
        audience: "https://ext-mock.dev.aerovy.com",
        additionalParameters: {
          prompt: "signup",
          screen_hint: "signup",
        },
      });
    } catch (e) {
      console.log(e);
    }
  };

  return <Button onPress={onPress} size={ButtonVariant.Size.LARGE} text="Sign up" flex={true} />;
};

const LoginButton = () => {
  const { authorize } = useAuth0();

  const onPress = async () => {
    try {
      await authorize({
        scope: "openid profile email",
        audience: "https://ext-mock.dev.aerovy.com",
      });
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <Button
      onPress={onPress}
      color={ButtonVariant.Color.PRIMARY}
      size={ButtonVariant.Size.LARGE}
      text="Log in"
      flex={true}
    />
  );
};

export default function Index() {
  const { user } = useAuth0();
  if (user) {
    return <Map />;
  }

  return (
    <View
      style={{
        flex: 1,
        flexDirection: "column",
        alignItems: "center",
        gap: 0,
        backgroundColor: "#fff",
        paddingHorizontal: 24,
        paddingVertical: 48,
      }}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Image source={require("@/assets/images/ekomoto logo.png")} />
      </View>
      <View style={{ flexDirection: "row", gap: 16 }}>
        <View style={{ flex: 1 }}>
          <SignupButton />
        </View>
        <View style={{ flex: 1 }}>
          <LoginButton />
        </View>
      </View>
    </View>
  );
}
