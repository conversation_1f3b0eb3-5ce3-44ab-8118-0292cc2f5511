import {
  ControlEventStatus,
  StartControlEventResponse,
  SwapStationStatus,
  confirmControlEvent,
  getSwapStationStatus,
  rentBattery,
  returnBattery,
  swapBattery,
} from "@/apis/enterprise/control";
import {
  Reservation,
  cancelReservation,
  createRentReservation,
  createSwapReservation,
  extendReservation,
  getReservationById,
  getReservations,
  updateReservationStatus,
} from "@/apis/enterprise/reservations";
import {
  Battery,
  BatteryDetail,
  SwapStation,
  forceRefresh,
  getBatteryDetail,
  getSwapStationBatteries,
  getSwapStationDetail,
} from "@/apis/enterprise/swapstation";
import i18next from "i18next";

export const ControlMiddleware = {
  getReservation: getReservationById,
  extendReservation: extendReservation,
  cancelReservation: cancelReservation,
  updateReservationStatus: updateReservationStatus,
  forceRefresh: forceRefresh,

  fullReturnAction: async (
    swapStation: SwapStation,
    customerId: string,
    token: string,
    setStatusText: (statusText: string) => void,
    setDoorId: (doorId: string) => void,
    overrides?: Partial<{ batteryManufacturerId: string }>,
  ) => {
    try {
      console.log("ControlMiddleware/fullReturnAction");
      setStatusText(i18next.t("uiPrompts.gettingBatteryDetails"));
      let oldBatteryManufacturerId;
      if (overrides?.batteryManufacturerId) {
        oldBatteryManufacturerId = overrides.batteryManufacturerId;
        console.log("ControlMiddleware/fullReturnAction: using overrides", oldBatteryManufacturerId);
      } else {
        oldBatteryManufacturerId = await ControlMiddleware.getCurrentBatteryManufacturerId(
          swapStation.partnerId,
          customerId,
          token,
        );
        console.log("ControlMiddleware/fullReturnAction: oldBatteryManufacturerId", oldBatteryManufacturerId);
      }

      if (!oldBatteryManufacturerId) {
        throw new Error("No current battery manufacturer id");
      }

      setStatusText(i18next.t("uiPrompts.gettingDoor"));
      const doorId = await ControlMiddleware.getEmptyDoorId(swapStation);
      console.log("ControlMiddleware/fullReturnAction: doorId", doorId);
      if (!doorId) {
        // TODO handle localization
        throw new Error("No empty door found");
      }

      setStatusText(i18next.t("uiPrompts.communicateStation1"));
      const returnResponse = await returnBattery(swapStation, doorId, oldBatteryManufacturerId, token);
      console.log("fullReturnAction: " + JSON.stringify(returnResponse));

      setStatusText(i18next.t("uiPrompts.communicateStation2"));
      await ControlMiddleware.doActionAgainst(swapStation, "return", returnResponse, token, console.log).then(() => {
        console.log("ControlMiddleware/fullReturnAction: setting doorId");
        setDoorId(doorId);
      });
    } catch (error) {
      throw new Error("fullReturnAction: " + JSON.stringify(error));
    }
  },

  fullSwapAction: async (
    swapStation: SwapStation,
    reservation: Reservation,
    customerId: string,
    token: string,
    setStatusText: (statusText: string) => void,
    setOldDoorId: (doorId: string) => void,
    setNewDoorId: (doorId: string) => void,
    overrides?: Partial<{ batteryManufacturerId: string }>,
  ) => {
    try {
      console.log("ControlMiddleware/fullSwapAction");
      setStatusText("Getting battery details...");

      let oldBatteryManufacturerId;
      if (overrides?.batteryManufacturerId) {
        oldBatteryManufacturerId = overrides.batteryManufacturerId;
        console.log("ControlMiddleware/fullSwapAction: using overrides", oldBatteryManufacturerId);
      } else {
        oldBatteryManufacturerId = await ControlMiddleware.getCurrentBatteryManufacturerId(
          swapStation.partnerId,
          customerId,
          token,
        );
        console.log("ControlMiddleware/fullSwapAction: oldBatteryManufacturerId", oldBatteryManufacturerId);
        if (!oldBatteryManufacturerId) {
          throw new Error("No current battery manufacturer id");
        }
      }

      setStatusText("Getting door...");
      const doorId = await ControlMiddleware.getEmptyDoorId(swapStation);
      console.log("ControlMiddleware/fullSwapAction: doorId", doorId);
      if (!doorId) {
        throw new Error("No empty door found");
      }
      setOldDoorId(doorId);

      setStatusText("Communicating with station (1)...");
      const swapResponse = await swapBattery(
        swapStation,
        doorId,
        reservation.reservedBatteryManufacturerId,
        reservation.reservedBatteryDoorId,
        oldBatteryManufacturerId,
        token,
      );
      console.log("fullSwapAction: " + JSON.stringify(swapResponse));

      setStatusText("Communicating with station (2)...");
      await ControlMiddleware.doActionAgainst(swapStation, "swap", swapResponse, token, console.log).then(() => {
        console.log("ControlMiddleware/fullSwapAction: setting doorId");
        setNewDoorId(reservation.reservedBatteryDoorId);
      });
    } catch (error) {
      setStatusText("Error please try again");
      throw new Error("fullSwapAction: " + JSON.stringify(error));
    }
  },

  fullInstallAction: async (
    swapStation: SwapStation,
    reservation: Reservation,
    token: string,
    setStatusText: (statusText: string) => void,
    setDoorId: (doorId: string) => void,
  ) => {
    setStatusText("Getting battery details...");
    const rentResponse = await rentBattery(swapStation, reservation, token);
    console.log("fullInstallAction: " + JSON.stringify(rentResponse));

    setStatusText("Communicating with station...");
    await ControlMiddleware.doActionAgainst(swapStation, "rent", rentResponse, token, console.log).then(() =>
      setDoorId(reservation.reservedBatteryDoorId),
    );
  },

  createRentReservationAction: async (partnerId: string, station: SwapStation, token: string): Promise<Reservation> => {
    const battery = await ControlMiddleware.determineBatteryToUse(partnerId, station);
    if (!battery) {
      throw new Error("No battery found");
    }
    console.log("ControlMiddleware/createRentReservation: new battery", battery);

    const reservation = await createRentReservation(station, battery, token);
    console.log("ControlMiddleware/createRentReservation: reservation", reservation);
    return reservation;
  },

  createSwapReservationAction: async (
    customerId: string,
    partnerId: string,
    station: SwapStation,
    token: string,
    overrides?: Partial<{ batteryManufacturerId: string }>,
  ): Promise<Reservation> => {
    console.log("ControlMiddleware/createSwapReservationAction");

    // get current reservation
    let currentBatteryManufacturerId;
    if (overrides?.batteryManufacturerId) {
      currentBatteryManufacturerId = overrides.batteryManufacturerId;
      console.log("ControlMiddleware/createSwapReservationAction: using overrides", currentBatteryManufacturerId);
    } else {
      currentBatteryManufacturerId = await ControlMiddleware.getCurrentBatteryManufacturerId(
        partnerId,
        customerId,
        token,
      );
      if (!currentBatteryManufacturerId) {
        throw new Error("No current battery manufacturer id");
      }
      console.log(
        "ControlMiddleware/createSwapReservationAction: currentBatteryManufacturerId",
        currentBatteryManufacturerId,
      );
    }

    const battery = await ControlMiddleware.determineBatteryToUse(partnerId, station);
    if (!battery) {
      throw new Error("No battery found");
    }
    console.log("ControlMiddleware/createSwapReservationAction: new battery", battery);

    const reservation = await createSwapReservation(station, currentBatteryManufacturerId, battery, token);
    console.log("ControlMiddleware/createSwapReservationAction: reservation", reservation);
    return reservation;
  },

  selectBestBattery: (batteries: Battery[]): Battery | null => {
    if (batteries.length === 0) {
      return null;
    }
    return batteries
      .filter((battery) => battery.soc >= 80)
      .sort((a, b) => {
        return b.soc - a.soc; // sort descending
      })[0];
  },
  determineBatteryToUse: async (partnerId: string, station: SwapStation): Promise<BatteryDetail | null> => {
    const batteries = await getSwapStationBatteries(partnerId, station.placeId, station.thingId);
    const bestBattery = ControlMiddleware.selectBestBattery(batteries);
    if (!bestBattery) {
      return null;
    }
    return await getBatteryDetail(partnerId, station.placeId, station.thingId, bestBattery.thingManufacturerId);
  },
  getLatestReservation: async (partnerId: string, customerId: string, token: string): Promise<Reservation | null> => {
    const tomorrow = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split("T")[0]; // YYYY-MM-DD
    const lastWeek = new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]; // YYYY-MM-DD
    const reservations = await getReservations(token, partnerId, customerId, {
      startTime: lastWeek,
      endTime: tomorrow,
    });
    const lastReservation = reservations.length > 0 ? reservations[reservations.length - 1] : null;

    // fetch detail because the fields are not all populated
    // TODO: fix this in the api
    if (lastReservation) {
      const { transactionId, placeType, placeId, thingId } = lastReservation;
      return await getReservationById(transactionId!, token, partnerId, placeType!, placeId!, thingId!);
    }
    return null;
  },
  getCurrentBatteryManufacturerId: async (
    partnerId: string,
    customerId: string,
    token: string,
  ): Promise<string | null> => {
    const reservation = await ControlMiddleware.getLatestReservation(partnerId, customerId, token);
    if (!reservation) {
      return null;
    }

    return reservation.reservedBatteryManufacturerId;

    // const { placeId, swapStationId, reservedBatteryManufacturerId } = reservation;
    // return await getBatteryDetail(partnerId, placeId, swapStationId, reservedBatteryManufacturerId);
  },

  getEmptyDoorId: async (swapStation: SwapStation): Promise<string | null> => {
    const stationData = await getSwapStationDetail(swapStation.partnerId, swapStation.placeId, swapStation.thingId);
    return stationData?.stationBoxStates.find((box) => box.stationBoxState === "NoBattery")?.stationBoxId || null;
  },

  doActionAgainst: async (
    swapStation: SwapStation,
    action: "rent" | "return" | "swap",
    response: StartControlEventResponse,
    token: string,
    dispatch: (action: string) => void,
  ) => {
    // poll getSwapStationStatus each second for 20 seconds until result.events[last].status === "Accepted"
    const result = await pollSwapStationStatus(swapStation, response.transactionId, token, dispatch);
    dispatch("getSwapStationStatus: " + JSON.stringify(result));

    if (!result) {
      throw new Error("No result from getSwapStationStatus");
    }

    // find the last event with status "AwaitingDeviceResponse". That should be the first event
    const referenceId = result.events.find((event) => event.status === "AwaitingDeviceResponse")?.referenceId;

    // confirm control event
    const confirmResponse = await confirmControlEvent(action, swapStation, response.transactionId, referenceId!, token);
    dispatch("confirmControlEvent: " + JSON.stringify(confirmResponse));
    const confirmResult = await pollSwapStationStatus(swapStation, response.transactionId, token, dispatch);
    dispatch("confirmControlEvent/status: " + JSON.stringify(confirmResult));
    if (!confirmResult) {
      throw new Error("No result from confirmControlEvent");
    }
  },
};

const POLL_INTERVAL = 1000;
const POLL_TIMES = 40;

const pollSwapStationStatus = async (
  swapStation: SwapStation,
  transactionId: string,
  token: string,
  logger: (log: string) => void,
): Promise<SwapStationStatus> => {
  let pollCount = 0;

  const poll = async (): Promise<SwapStationStatus> => {
    try {
      const result = await getSwapStationStatus(swapStation, transactionId, token);
      const lastEvent = result.events[result.events.length - 1];
      logger(`getSwapStationStatus: (${pollCount} / ${POLL_TIMES}) ${JSON.stringify(result.events)}`);

      if (lastEvent?.status === ControlEventStatus.Accepted) {
        return result;
      }

      pollCount++;
      if (pollCount >= POLL_TIMES) {
        throw new Error("Polling timed out");
      }

      return new Promise((resolve) => {
        setTimeout(async () => {
          resolve(await poll());
        }, POLL_INTERVAL);
      });
    } catch (error) {
      throw error;
    }
  };

  return poll();
};
