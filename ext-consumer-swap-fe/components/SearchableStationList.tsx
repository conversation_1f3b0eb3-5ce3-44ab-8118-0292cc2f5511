import { FontStyles } from "@/constants/FontStyles";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import {
  FindByChargeLevelProvider,
  FindByChargeLevelSelector,
  useFindByChargeLevelContext,
} from "@/contexts/useFindByChargeLevelContext";
import "@/extensions/number+toKm";
import { useDebounceState } from "@/hooks/useDebounceState";
import { EnhancedSwapStation } from "@/hooks/useSwapStationMiddleware";
import { getColor } from "@/utils/style";
import { useRouter } from "expo-router";
import { ChevronRight } from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { ActivityIndicator, ScrollView, Text, TouchableOpacity } from "react-native";
import { View } from "react-native";

import { FilterButton } from "./FilterButton";
import { SearchInput } from "./SearchInput";
import { SnappableBottomSheet } from "./SnappableBottomSheet";

export const StationRow = ({ station }: { station: EnhancedSwapStation }) => {
  const { maxDistance } = useFindByChargeLevelContext();
  const { t } = useTranslation();

  const isInRange = station.distance && station.distance <= maxDistance;
  const distanceColor = isInRange ? getColor("primary") : getColor("destructive");
  const backgroundColor = isInRange ? getColor("primary", "dim") : getColor("destructive", "dim");

  const distanceView = (
    <View
      style={{
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: backgroundColor,
        borderRadius: 16,
        width: 48,
        height: 48,
      }}
    >
      <Text style={[FontStyles.bodyEmphasized, { color: distanceColor }]}>{station.distance?.toKm() ?? "-"}</Text>
      <Text style={[FontStyles.footnote, { color: distanceColor }]}>KM</Text>
    </View>
  );

  return (
    <View
      style={{
        flex: 1,
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
        marginBottom: 16,
      }}
    >
      {distanceView}
      <View style={{ flexDirection: "column", gap: 0 }}>
        <Text style={[FontStyles.heading3, { color: getColor("foreground") }]}>{station.thingName}</Text>
        <View style={{ flexDirection: "row", gap: 4 }}>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {station.batteryManufacturerIds?.length ?? "-"} {t("uiPrompts.batteriesAvailable")}
          </Text>
          {!isInRange && (
            <Text style={[FontStyles.caption, { color: getColor("destructive") }]}>({t("uiPrompts.outOfRange")})</Text>
          )}
        </View>
      </View>
      <ChevronRight size={16} color={getColor("foreground")} style={{ marginLeft: "auto" }} />
    </View>
  );
};

export const StationList = ({ stations }: { stations: EnhancedSwapStation[] }) => {
  const router = useRouter();
  return (
    <>
      {stations.map((station) => (
        <TouchableOpacity
          key={station.thingId}
          onPress={() => {
            router.push(`/station/${station.thingId}`);
          }}
        >
          <StationRow station={station} />
        </TouchableOpacity>
      ))}
    </>
  );
};

const FilteredList = () => {
  // TODO: add filters
  const [search, debouncedSearch, setSearch] = useDebounceState("", 300);

  const { stations, loading, error } = useEnhancedSwapStation();

  const filteredStations = stations.filter((station) =>
    debouncedSearch ? station.thingName.toLowerCase().includes(debouncedSearch) : true,
  );

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" color={getColor("primary")} />
        <Text style={[FontStyles.body, { marginTop: 16, color: getColor("foreground") }]}>
          Loading stations...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: 16 }}>
        <Text style={[FontStyles.body, { color: getColor("destructive"), textAlign: "center" }]}>
          Error loading stations: {error}
        </Text>
      </View>
    );
  }

  return (
    <View style={{ paddingHorizontal: 16, gap: 16 }}>
      <SearchInput search={search} setSearch={setSearch} />
      <FindByChargeLevelSelector />
      {/* <ScrollView horizontal>
          <View style={{ flexDirection: "row", gap: 8, marginVertical: 16 }}>
            <FilterButton
              text="Available"
              value={available}
              onPress={setAvailable}
            />
            <FilterButton
              text="# Batteries"
              value={numBatteries}
              onPress={setNumBatteries}
            />
            <FilterButton
              text="Distance"
              value={distance}
              onPress={setDistance}
            />
            <FilterButton text="Price" value={price} onPress={setPrice} />
          </View>
        </ScrollView> */}
      <StationList stations={filteredStations} />
    </View>
  );
};

export const SearchableStationList = ({
  initialHeight,
  distanceFromTop,
}: {
  initialHeight: number;
  distanceFromTop: number;
}) => {
  // const [available, setAvailable] = useState<boolean>(false);
  // const [numBatteries, setNumBatteries] = useState<number | null>(null);
  // const [distance, setDistance] = useState<number | null>(null);
  // const [price, setPrice] = useState<number | null>(null);

  // TODO: use a hook to filter stations since it'll probably be computationally expensive
  // TODO: add thingName, address, thingDescription to search

  // .filter((station) => (available ? station.availableBatteries > 0 : true))
  // .filter((station) =>
  //   numBatteries ? station.availableBatteries >= numBatteries : true
  // )
  // .filter((station) => (distance ? station.distance <= distance : true));
  // .filter((station) => (price ? station.price <= price : true));

  // TODO: add loading and error states
  return (
    <SnappableBottomSheet initialHeight={initialHeight} distanceFromTop={distanceFromTop} additionalSnapPoints={[300]}>
      <FindByChargeLevelProvider>
        <FilteredList />
      </FindByChargeLevelProvider>
    </SnappableBottomSheet>
  );
};
