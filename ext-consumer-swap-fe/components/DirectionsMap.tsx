import { DirectionsRoute, getBoundingBox } from "@/apis/mapbox";
import useResetLocation from "@/hooks/useResetLocation";
import { getColor } from "@/utils/style";
import Mapbox, { Camera, LineLayer, MapView, PointAnnotation, ShapeSource } from "@rnmapbox/maps";

import React, { useEffect, useRef, useState } from "react";
import { View } from "react-native";
import { EdgeInsets } from "react-native-safe-area-context";

import { CurrentLocationView } from "./CurrentLocationView";

Mapbox.setAccessToken(process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN);
Mapbox.setTelemetryEnabled(false);

const lineLayerStyle = {
  lineColor: getColor("primary"),
  lineWidth: 4,
};

export const DirectionsMap = ({
  insets,
  from,
  to,
  directions,
}: {
  insets: EdgeInsets;
  from?: { latitude: number; longitude: number } | null;
  to: { latitude: number; longitude: number };
  directions: DirectionsRoute | null;
}) => {
  const cameraRef = useRef<Camera>(null);
  const { top, left, bottom, right } = insets;
  const [isMapReady, setIsMapReady] = useState(false);

  const { ResetLocationButton } = useResetLocation({ top, cameraRef });

  useEffect(() => {
    if (!directions || !isMapReady || !cameraRef.current) return;

    const timer = setTimeout(() => {
      const boundingBox = getBoundingBox(directions.geometry.coordinates);
      cameraRef.current?.fitBounds(boundingBox[0], boundingBox[1], [50, 50], 1000);
    }, 100);

    return () => clearTimeout(timer);
  }, [directions, isMapReady]);

  return (
    <View style={{ flex: 1 }}>
      <MapView
        style={{ flex: 1 }}
        projection="mercator"
        pitchEnabled={false}
        rotateEnabled={false}
        attributionPosition={{ bottom, right }}
        scaleBarPosition={{ left, bottom }}
        styleURL="mapbox://styles/aerovy-it/cm2wglral00bl01qkd56x4ud5"
        onDidFinishLoadingMap={() => {
          setIsMapReady(true);
        }}
      >
        <Camera
          ref={cameraRef}
          defaultSettings={{
            zoomLevel: 14,
            centerCoordinate: [to.longitude, to.latitude],
          }}
          padding={{
            paddingTop: top,
            paddingLeft: left,
            paddingBottom: bottom,
            paddingRight: right,
          }}
        />
        {to && (
          <PointAnnotation id="to" coordinate={[to.longitude, to.latitude]} title="Station">
            <View
              style={{
                backgroundColor: getColor("primary"),
                width: 16,
                height: 16,
                borderRadius: 8,
              }}
            />
          </PointAnnotation>
        )}
        {from && (
          <PointAnnotation id="from" coordinate={[from.longitude, from.latitude]} title="Current location">
            <CurrentLocationView />
          </PointAnnotation>
        )}
        {directions && (
          <ShapeSource id={"shape-source-id-0"} shape={directions.geometry}>
            <LineLayer id={"line-layer"} style={lineLayerStyle} />
          </ShapeSource>
        )}
      </MapView>
      <ResetLocationButton />
    </View>
  );
};
