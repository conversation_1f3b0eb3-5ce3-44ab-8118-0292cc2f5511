import { BatteryDetail, SwapStation } from "./swapstation";

const API_URL = "https://spectra.api.dev.aerovy.com";

// {
//     "swapStationId": "6ed6b55a-1692-4e35-bed0-0dba9256a2e7",
//     "swapStationManufacturerId": "HD0551",
//     "reservedBatteryManufacturerId": "BT207203012HBDJ240800050",
//     "reservedBatteryId": "87a8e63e-771a-4172-9314-c8652efa8af3",
//     "reservedBatteryDoorId": "1",
//     "expirationTime": "2024-11-19T00:20:50.2878579Z",
//     "renewalCount": 0,
//     "partnerId": "00000000-ad9d-4025-81ab-e6083532bb7b",
//     "externalPartnerId": "00000000-00c6-4bb4-9eab-188ec43c0806",
//     "ownerId": "TESTCUSTOMER",
//     "transactionId": "dd734483-1f26-4c51-b307-0318bf7c51b5",
//     "transactionType": "BatterySwapReservation",
//     "transactionStatus": "IN_PROGRESS",
//     "thingId": "6ed6b55a-1692-4e35-bed0-0dba9256a2e7",
//     "placeId": "ac04bbcf-86ba-4916-8f63-8f3d8a63b397",
//     "placeType": "SITE",
//     "metrics": {},
//     "createdAt": "2024-11-18T23:20:50.2881145Z",
//     "createdBy": "00000000-00c6-4bb4-9eab-188ec43c0806.TESTCUSTOMER"
// }

export type ReservationStatus = "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED" | "EXPIRED";

export type Reservation = {
  swapStationId: string;
  swapStationManufacturerId: string;
  reservedBatteryManufacturerId: string;
  reservedBatteryId: string;
  reservedBatteryDoorId: string;
  expirationTime: string;
  renewalCount: number;
  partnerId: string;
  externalPartnerId: string;
  ownerId: string;
  transactionId: string;
  transactionType: string;
  transactionStatus: ReservationStatus;
  thingId: string;
  placeId: string;
  placeType: string;
  // metrics: {};  // TODO: what is this? GenericAttributes?
  createdAt: string;
  createdBy: string;
};

// partnerId + customerId + startTime + endTime is enough
export const getReservations = async (
  accessToken: string,
  partnerId: string,
  customerId: string,
  options: Partial<{
    placeId: string;
    thingId: string;
    startTime: string;
    endTime: string;
    placeType: string;
    transactionStatus: ReservationStatus;
  }>,
): Promise<Partial<Reservation>[]> => {
  const { placeId, thingId, startTime, endTime, placeType, transactionStatus } = options;

  const queryParams = new URLSearchParams();
  queryParams.set("partnerId", partnerId);
  queryParams.set("customerId", customerId);
  if (placeId) queryParams.set("placeId", placeId);
  if (thingId) queryParams.set("thingId", thingId);
  if (startTime) queryParams.set("startTime", startTime);
  if (endTime) queryParams.set("endTime", endTime);
  if (placeType) queryParams.set("placeType", placeType);
  if (transactionStatus) queryParams.set("transactionStatus", transactionStatus);

  const response = await fetch(`${API_URL}/reservation/transactions?${queryParams.toString()}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response.json();
};

// TODO: what are the return types?
// TODO: dont use DELETE for cancel reservations

// TODO: why are these all required?
export const getReservationById = async (
  transactionId: string,
  accessToken: string,
  partnerId: string,
  placeType: string,
  placeId: string,
  thingId: string,
): Promise<Reservation> => {
  const params = new URLSearchParams();
  params.set("partnerId", partnerId);
  params.set("placeType", placeType);
  params.set("placeId", placeId);
  params.set("thingId", thingId);
  const response = await fetch(`${API_URL}/reservation/transactions/${transactionId}?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response.json();
};

export const extendReservation = async (
  transactionId: string,
  accessToken: string,
  partnerId: string,
  placeType: string,
  placeId: string,
  thingId: string,
): Promise<Reservation> => {
  const params = new URLSearchParams();
  params.set("partnerId", partnerId);
  params.set("placeType", placeType);
  params.set("placeId", placeId);
  params.set("thingId", thingId);
  const response = await fetch(`${API_URL}/reservation/transactions/${transactionId}/extend?${params.toString()}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to extend reservation: ${error.detail}`);
  }
  return response.json();
};

export const updateReservationStatus = async (
  transactionId: string,
  status: ReservationStatus,
  accessToken: string,
  partnerId: string,
  placeType: string,
  placeId: string,
  thingId: string,
): Promise<string> => {
  const params = new URLSearchParams();
  params.set("partnerId", partnerId);
  params.set("placeType", placeType);
  params.set("placeId", placeId);
  params.set("thingId", thingId);
  const response = await fetch(
    `${API_URL}/reservation/transactions/${transactionId}/status/${status}?${params.toString()}`,
    {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    },
  );

  const responseText = await response.text();
  console.log("updateReservationStatus: response", response.status, responseText);
  if (!response.ok) {
    throw new Error(`Failed to update reservation status: ${responseText}`);
  }
  return responseText;
};

export const cancelReservation = async (
  transactionId: string,
  accessToken: string,
  partnerId: string,
  placeType: string,
  placeId: string,
  thingId: string,
): Promise<string> => {
  return updateReservationStatus(transactionId, "CANCELLED", accessToken, partnerId, placeType, placeId, thingId);
};

export const deleteReservation = async (transactionId: string, accessToken: string) => {
  const response = await fetch(`${API_URL}/reservation/transactions/${transactionId}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response.json();
};

export const createRentReservation = async (
  swapStation: SwapStation,
  battery: BatteryDetail,
  accessToken: string,
): Promise<Reservation> => {
  const response = await fetch(`${API_URL}/reservation/transactions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      swapStationPartnerId: swapStation.partnerId,
      swapStationThingId: swapStation.thingId,
      swapStationPlaceId: swapStation.placeId,
      swapStationDoorId: battery.boxState.stationBoxId,
      batteryManufacturerId: battery.batteryState.thingManufacturerId,
    }),
  });
  return response.json();
};

export const createSwapReservation = async (
  swapStation: SwapStation,
  currentBatteryManufacturerId: string,
  newBattery: BatteryDetail,
  accessToken: string,
): Promise<Reservation> => {
  // TODO: should pass in currentBatteryManufacturerId
  const response = await fetch(`${API_URL}/reservation/transactions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      swapStationPartnerId: swapStation.partnerId,
      swapStationThingId: swapStation.thingId,
      swapStationPlaceId: swapStation.placeId,
      swapStationDoorId: newBattery.boxState.stationBoxId,
      batteryManufacturerId: newBattery.batteryState.thingManufacturerId,
    }),
  });
  return response.json();
};
