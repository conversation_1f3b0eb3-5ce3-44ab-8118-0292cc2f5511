import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import useResetLocation from "@/hooks/useResetLocation";
import { getColor } from "@/utils/style";
import Mapbox, { Camera, Image, Images, LocationPuck, MapView } from "@rnmapbox/maps";

import React, { useEffect, useRef, useState } from "react";
import { View } from "react-native";
import { EdgeInsets } from "react-native-safe-area-context";

import { CurrentLocationView } from "./CurrentLocationView";

// Default fallback coordinates (Jakarta, Indonesia)
const DEFAULT_COORDINATES = {
  latitude: -6.2088,
  longitude: 106.8456,
};

Mapbox.setAccessToken(process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN || "");
Mapbox.setTelemetryEnabled(false);

export const Map = ({ insets, children }: { insets: EdgeInsets; children: React.ReactNode }) => {
  const { top, left, bottom, right } = insets;
  const [isMapReady, setIsMapReady] = useState(false);

  const cameraRef = useRef<Camera>(null);

  const { ResetLocationButton } = useResetLocation({ top, cameraRef });
  const { location, error: locationError } = useUserLocationContext();

  // Use location if available, otherwise use default coordinates
  const mapCenter = location || DEFAULT_COORDINATES;

  useEffect(() => {
    // Only update camera if map is ready and we have valid coordinates
    if (isMapReady && mapCenter && cameraRef.current) {
      const timer = setTimeout(() => {
        cameraRef.current?.setCamera({
          centerCoordinate: [mapCenter.longitude, mapCenter.latitude],
          zoomLevel: 14,
          animationDuration: location ? 1000 : 0, // Animate only when user location is available
        });
      }, 100); // Small delay to ensure map is fully initialized

      return () => clearTimeout(timer);
    }
  }, [isMapReady, mapCenter, location]);

  return (
    <View style={{ flex: 1 }}>
      <MapView
        style={{ flex: 1 }}
        projection="mercator"
        pitchEnabled={false}
        rotateEnabled={false}
        attributionPosition={{ bottom, right }}
        scaleBarPosition={{ left, bottom }}
        styleURL="mapbox://styles/aerovy-it/cm2wglral00bl01qkd56x4ud5"
        onDidFinishLoadingMap={() => {
          setIsMapReady(true);
        }}
      >
        <Images>
          <Image name="topImage">
            <CurrentLocationView />
          </Image>
        </Images>
        <Camera
          ref={cameraRef}
          defaultSettings={{
            centerCoordinate: [mapCenter.longitude, mapCenter.latitude],
            zoomLevel: 14,
          }}
          padding={{
            paddingTop: top,
            paddingLeft: left,
            paddingBottom: bottom,
            paddingRight: right,
          }}
        />
        <LocationPuck
          topImage="topImage"
          visible={!!location} // Only show location puck when we have actual user location
          scale={1.0}
          pulsing={{
            isEnabled: true,
            color: getColor("primary"),
            radius: 32,
          }}
        />
        {children}
      </MapView>
      <ResetLocationButton />
    </View>
  );
};
