name: Build Release AAB and Upload

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./ext-consumer-swap-fe

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate Version Code
        id: version_code
        run: |
          VERSION_CODE=$(git rev-list --count HEAD)
          echo "version_code=$VERSION_CODE" >> "$GITHUB_OUTPUT"
          echo "version_name=1.0.$VERSION_CODE" >> "$GITHUB_OUTPUT"
          echo "Generated version code: $VERSION_CODE"
          echo "Generated version name: 1.0.$VERSION_CODE"

      - name: Decode Keystore
        id: decode_keystore
        uses: timheuer/base64-to-file@v1
        with:
          fileName: 'ext-consumer-swap-fe-upload-key.keystore'
          encodedString: ${{ secrets.UPLOAD_KEY_BASE_64 }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Set up JD<PERSON>
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'zulu'
          cache: gradle

      - name: Build Release AAB
        run: npx react-native build-android --mode=release
        env:
          UPLOAD_STORE_FILE: ${{ steps.decode_keystore.outputs.filePath }}
          UPLOAD_KEY_ALIAS: ${{ vars.UPLOAD_KEY_ALIAS }}
          UPLOAD_STORE_PASSWORD: ${{ secrets.UPLOAD_STORE_PASSWORD }}
          UPLOAD_KEY_PASSWORD: ${{ secrets.UPLOAD_KEY_PASSWORD }}
          MAPBOX_DOWNLOADS_TOKEN: ${{ secrets.MAPBOX_DOWNLOADS_TOKEN }}
          EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN: ${{ vars.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN }}
          VERSION_CODE: ${{ steps.version_code.outputs.version_code }}
          VERSION_NAME: ${{ steps.version_code.outputs.version_name }}

      - name: Upload AAB Artifact
        uses: actions/upload-artifact@v4
        with:
          name: app-release-bundle
          path: ext-consumer-swap-fe/android/app/build/outputs/bundle/release/app-release.aab
