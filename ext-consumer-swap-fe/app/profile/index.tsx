import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { FontStyles } from "@/constants/FontStyles";
import { type Transaction, transactions } from "@/static/transactions";
import { getColor } from "@/utils/style";
import { useRouter } from "expo-router";
import {
  ArrowLeftRight,
  Bell,
  ChevronRight,
  Clock,
  KeyRound,
  Landmark,
  Languages,
  SquareArrowOutUpRight,
  X,
} from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useAuth0 } from "react-native-auth0";
import { SafeAreaView } from "react-native-safe-area-context";

const styles = StyleSheet.create({
  safeArea: { flex: 1, width: "100%", backgroundColor: getColor("background") },

  container: {
    flex: 1,
    flexDirection: "column",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    paddingHorizontal: 16,
    gap: 32,
    paddingBottom: 32,
  },
});

const StoredValue = () => {
  const { t } = useTranslation();
  return (
    <View
      style={{
        width: "100%",
        justifyContent: "center",
        alignItems: "center",
        paddingTop: 48,
        paddingBottom: 32,
        gap: 16,
        borderRadius: 16,
        backgroundColor: getColor("primary", "dim"),
      }}
    >
      <View style={{ gap: 4, alignItems: "center" }}>
        <View style={{ flexDirection: "row", gap: 0 }}>
          <Text style={[FontStyles.heading2, { color: getColor("primary") }]}>Rp</Text>
          <Text style={[FontStyles.title, { color: getColor("primary") }]}>1.000.000</Text>
        </View>
        <Text style={[FontStyles.caption, { color: getColor("primary") }]}>{t("uiPrompts.availableBalance")}</Text>
      </View>
      <Button text={t("uiPrompts.addMoney")} disabled />
    </View>
  );
};

const TransactionRow = ({ transaction }: { transaction: Transaction }) => {
  const router = useRouter();
  const icon = () => {
    if (transaction.status === "Completed") return <ArrowLeftRight color={getColor("primary")} size={24} />;
    if (transaction.status === "Cancelled") return <X color={getColor("foreground")} size={24} />;
    return <Clock color={getColor("foreground")} size={24} />;
  };

  const backgroundColor = () => {
    if (transaction.status === "Completed") return "rgba(28, 160, 31, 0.05)";
    return "rgba(17, 39, 18, 0.05)";
  };

  return (
    <TouchableOpacity
      onPress={() => {
        router.push(`/receipt/${transaction.id}`);
      }}
    >
      <View
        style={{
          flexDirection: "row",
          gap: 8,
          justifyContent: "flex-start",
          alignItems: "center",
          flex: 1,
        }}
      >
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 48,
            width: 48,
            borderRadius: 16,
            backgroundColor: backgroundColor(),
          }}
        >
          {icon()}
        </View>
        <View style={{ flexDirection: "column", gap: 4, flex: 1 }}>
          <View
            style={{
              flexDirection: "row",
              gap: 0,
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={[FontStyles.heading3, { color: getColor("foreground") }]}>{transaction.location}</Text>
            <ChevronRight color={getColor("foreground")} size={16} />
          </View>
          <View
            style={{
              flexDirection: "row",
              gap: 0,
              alignItems: "center",
              justifyContent: "space-between",
              opacity: 0.5,
            }}
          >
            <Text style={[FontStyles.caption, { color: getColor("foreground") }]}>
              {transaction.date.toLocaleString()}
            </Text>
            <Text style={[FontStyles.caption, { color: getColor("foreground") }]}>
              Rp{transaction.amount.toLocaleString()}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const TransactionHistory = () => {
  const { t } = useTranslation();
  return (
    <View style={{ gap: 16, flex: 1, width: "100%" }}>
      <Text style={FontStyles.heading2}>{t("uiPrompts.transactionHistory")}</Text>
      {transactions.map((transaction) => (
        <TransactionRow key={transaction.id} transaction={transaction} />
      ))}
    </View>
  );
};

const LinkRow = ({ icon, title, onPress }: { icon: React.ReactNode; title: string; onPress?: () => void }) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <View
        style={{
          flexDirection: "row",
          gap: 8,
          justifyContent: "flex-start",
          alignItems: "center",
          flex: 1,
        }}
      >
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 48,
            width: 48,
            borderRadius: 16,
            backgroundColor: "rgba(28, 160, 31, 0.05)",
          }}
        >
          {icon}
        </View>
        <View style={{ flexDirection: "column", gap: 4, flex: 1 }}>
          <View
            style={{
              flexDirection: "row",
              gap: 0,
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={[FontStyles.heading3, { color: getColor("foreground") }]}>{title}</Text>
            <ChevronRight color={getColor("foreground")} size={16} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const ExternalLinkRow = ({ title }: { title: string }) => {
  return (
    <View style={{ flexDirection: "row", gap: 8, alignItems: "center" }}>
      <Text
        style={[
          FontStyles.link,
          {
            color: getColor("primary"),
            textDecorationLine: "underline",
          },
        ]}
      >
        {title}
      </Text>
      <SquareArrowOutUpRight size={16} color={getColor("primary")} />
    </View>
  );
};

const AccountSettings = () => {
  const router = useRouter();
  const { t } = useTranslation();
  return (
    <View style={{ gap: 16, flex: 1, width: "100%" }}>
      <Text style={FontStyles.heading2}>{t("uiPrompts.accountSettings")}</Text>
      <LinkRow icon={<KeyRound size={24} color={getColor("primary")} />} title={t("uiPrompts.loginAndPassword")} />
      <LinkRow icon={<Landmark size={24} color={getColor("primary")} />} title={t("uiPrompts.billing")} />
      <LinkRow icon={<Bell size={24} color={getColor("primary")} />} title={t("uiPrompts.notificationSettings")} />
      <LinkRow
        icon={<Languages size={24} color={getColor("primary")} />}
        title={t("uiPrompts.languages")}
        onPress={() => router.push("/profile/languages")}
      />
    </View>
  );
};

const Help = () => {
  const { t } = useTranslation();
  return (
    <View style={{ gap: 16, flex: 1, width: "100%" }}>
      <Text style={FontStyles.heading2}>{t("uiPrompts.help")}</Text>
      <ExternalLinkRow title={t("uiPrompts.contactUs")} />
      <ExternalLinkRow title={t("uiPrompts.termsOfService")} />
      <ExternalLinkRow title={t("uiPrompts.privacyPolicy")} />
    </View>
  );
};

const LogoutButton = () => {
  const { clearSession } = useAuth0();
  const router = useRouter();
  const { t } = useTranslation();

  const onPress = async () => {
    try {
      await clearSession();
      router.replace("/");
    } catch (e) {
      console.log(e);
    }
  };

  return <Button onPress={onPress} color={ButtonVariant.Color.DESTRUCTIVE} text={t("uiPrompts.logout")} />;
};

export default function Profile() {
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView>
        <View style={styles.container}>
          <CloseButton
            style={{
              width: "100%",
              flexDirection: "row-reverse",
              justifyContent: "flex-start",
            }}
          />
          <StoredValue />
          <TransactionHistory />
          <AccountSettings />
          <Help />
          <LogoutButton />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
