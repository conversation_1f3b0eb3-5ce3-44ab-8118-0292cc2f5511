/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

export const opacityHexCode = {
  full: "FF",
  half: "80",
  dim: "0D",
};

export const ColorsMap = {
  light: {
    primary: "#1CA01F",
    destructive: "#A01F1C",
    foreground: "#112712",
    background: "#FFFFFF",
  },
  // TODO: dark
  dark: {
    primary: "#1CA01F",
    destructive: "#A01F1C",
    foreground: "#112712",
    background: "#FFFFFF",
  },
};

export type OpacityLevel = keyof typeof opacityHexCode;
export type ColorMapLightKeys = keyof typeof ColorsMap.light;
