import { Reservation } from "@/apis/enterprise/reservations";
import { fetchDirections } from "@/apis/mapbox";
import { DirectionsRoute } from "@/apis/mapbox";
import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { DirectionsMap } from "@/components/DirectionsMap";
import { NavigateButton } from "@/components/NavigateButton";
import { SnappableBottomSheet } from "@/components/SnappableBottomSheet";
import { FontStyles } from "@/constants/FontStyles";
import { useApiFieldContext } from "@/contexts/apiFieldContext";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { EnhancedSwapStation } from "@/hooks/useSwapStationMiddleware";
import { languageBasedOnActionMap } from "@/locales/map";
import { ControlMiddleware } from "@/middleware/controlMiddleware";
import { ErrorHandlingMiddleware } from "@/middleware/errorHandlingMiddleware";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";
import { ClockArrowUp, CornerUpRight } from "lucide-react-native";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, Text, TouchableOpacity, View, useWindowDimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const ReservationDetailView = ({
  action,
  station,
  reservation,
  setReservation,
}: {
  action: "swap" | "rent";
  station: EnhancedSwapStation;
  reservation: Reservation;
  setReservation: (reservation: Reservation) => void;
}) => {
  const [extendLoading, setExtendLoading] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);

  const { t, i18n } = useTranslation();

  const { token, partnerId, setReservation: setApiFieldContextReservation, setSwapStation } = useApiFieldContext();
  const { placeId, placeType, thingId } = station;

  const extendReservation = async () => {
    setExtendLoading(true);

    await ControlMiddleware.extendReservation(reservation.transactionId, token, partnerId, placeType, placeId, thingId)
      .then(setReservation)
      .catch((error) => {
        console.error("extendReservation: error", error);
        ErrorHandlingMiddleware.showPopupMessage(error.message);
      });

    setExtendLoading(false);
  };

  const cancelReservation = async () => {
    setCancelLoading(true);
    await ControlMiddleware.cancelReservation(reservation.transactionId, token, partnerId, placeType, placeId, thingId)
      .then(() => {
        // refresh the reservation
        ControlMiddleware.getReservation(reservation.transactionId, token, partnerId, placeType, placeId, thingId).then(
          setReservation,
        );
      })
      .catch((error) => {
        console.error("cancelReservation: error", error);
        ErrorHandlingMiddleware.showPopupMessage(error.message);
      })
      .finally(() => {
        setCancelLoading(false);
      });
  };

  // TODO: reuse the same drawer component between stationid, new reservation, and reservation detail instead of duplicating the code

  return (
    <View style={{ flex: 1, padding: 16, gap: 32 }}>
      <CloseButton position="absolute" />

      {/* Reservation Details */}
      <View style={{ flexDirection: "column", gap: 16 }}>
        {/* Station Info */}
        <View style={{ flexDirection: "column", gap: 4 }}>
          <Text style={[FontStyles.heading1, { color: getColor("foreground") }]}>
            {t("uiPrompts.myReservationAction", {
              action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
            })}
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>{station.thingName}</Text>
        </View>

        {/* Reservation Location */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            gap: 16,
          }}
        >
          <View style={{ flexDirection: "column", gap: 0, flexShrink: 1 }}>
            <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
              {t("uiPrompts.reservationLocation")}
            </Text>
            <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{station.address}</Text>
          </View>

          <View>
            <NavigateButton latitude={station.latitude} longitude={station.longitude} />
          </View>
        </View>
        {/* Reservation End Time */}
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <View style={{ flexDirection: "column", gap: 0 }}>
            <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
              {t("uiPrompts.reservationEndTime")}
            </Text>
            <Text style={[FontStyles.body, { color: getColor("foreground") }]}>
              {new Date(reservation.expirationTime).toLocaleString()}
            </Text>
          </View>
          <View>
            {extendLoading ? (
              <ActivityIndicator />
            ) : (
              reservation.transactionStatus === "IN_PROGRESS" && (
                <Button
                  text={t("uiPrompts.extend")}
                  onPress={extendReservation}
                  size={ButtonVariant.Size.MEDIUM}
                  color={ButtonVariant.Color.SECONDARY}
                  icon={<ClockArrowUp size={16} color={getColor("primary")} />}
                />
              )
            )}
          </View>
        </View>
        {/* Reservation Price */}
        {/* <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color:getColor('foreground', 'half') }]}>
            Reservation Price
          </Text>
          <Text style={[FontStyles.body, { color: getColor('foreground') }]}>
            Rp{reservation.price.toLocaleString()}
          </Text>
        </View> */}
        {/* Reservation Battery */}
        <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {t("uiPrompts.reservedBatteryDoor")}
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{reservation.reservedBatteryDoorId}</Text>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {t("uiPrompts.batteryId")}: {reservation.reservedBatteryManufacturerId}
          </Text>
        </View>
        {/* Reservation Status */}
        <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {t("uiPrompts.reservationStatus")}
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{reservation.transactionStatus}</Text>
        </View>
        {/* Reservation ID and Hold */}
        <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {t("uiPrompts.reservationId")}: {reservation.transactionId}
          </Text>
          {/* <Text style={[FontStyles.caption, { color: getColor('foreground', 'half') }]}>
            Last Updated: 1 minute ago
          </Text> */}
        </View>
      </View>

      {/* CTA */}
      {reservation.transactionStatus === "IN_PROGRESS" && (
        <Button
          text={t("uiPrompts.imHere")}
          onPress={() => {
            setApiFieldContextReservation(reservation);
            setSwapStation(station);
            router.replace(`/reservation/control/start?transactionId=${reservation.transactionId}&action=${action}`);
          }}
          size={ButtonVariant.Size.LARGE}
          color={ButtonVariant.Color.PRIMARY}
        />
      )}

      {/* TODO: cancel reservation */}
      <View style={{ alignItems: "center" }}>
        {cancelLoading ? (
          <ActivityIndicator />
        ) : (
          reservation.transactionStatus === "IN_PROGRESS" && (
            <TouchableOpacity onPress={cancelReservation}>
              <Text
                style={[
                  FontStyles.heading3,
                  {
                    color: getColor("destructive"),
                    textDecorationLine: "underline",
                  },
                ]}
              >
                {t("uiPrompts.cancelReservation")}
              </Text>
            </TouchableOpacity>
          )
        )}
      </View>
    </View>
  );
};

export default function ReservationDetail() {
  const { id, placeId, placeType, thingId, action } = useLocalSearchParams();
  const { stations } = useEnhancedSwapStation();
  const { token, partnerId } = useApiFieldContext();

  const [reservation, setReservation] = useState<Reservation | null>(null);

  const station = stations.find((station) => station.thingId === reservation?.swapStationId);

  const { location } = useUserLocationContext();
  const [directions, setDirections] = useState<DirectionsRoute | null>(null);

  useEffect(() => {
    if (!location || !station) return;
    const fetchDirectionsAsync = async () => {
      const directions = await fetchDirections(location, stationLocation);
      setDirections(directions);
    };
    fetchDirectionsAsync();
  }, [station, location]);

  useEffect(() => {
    if (!id) return;
    ControlMiddleware.getReservation(id, token, partnerId, placeType, placeId, thingId).then((reservation) => {
      setReservation(reservation);
    });
  }, [id]);

  const insets = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  const initialBottomSheetHeight = (height - insets.top - insets.bottom) / 2;

  if (!station) {
    return null;
  }

  const stationLocation = {
    latitude: station.latitude,
    longitude: station.longitude,
  };

  return (
    <View style={{ flex: 1 }}>
      <DirectionsMap
        from={location}
        to={stationLocation}
        directions={directions}
        insets={{
          top: insets.top,
          bottom: initialBottomSheetHeight + 16,
          left: insets.left + 16,
          right: insets.right + 16,
        }}
      />
      <SnappableBottomSheet initialHeight={initialBottomSheetHeight}>
        {reservation ? (
          <ReservationDetailView
            action={action}
            station={station}
            reservation={reservation}
            setReservation={setReservation}
          />
        ) : (
          <ActivityIndicator />
        )}
      </SnappableBottomSheet>
    </View>
  );
}
