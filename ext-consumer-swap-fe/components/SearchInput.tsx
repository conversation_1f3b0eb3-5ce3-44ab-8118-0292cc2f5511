import { FontStyles } from "@/constants/FontStyles";
import { getColor } from "@/utils/style";
import { Search } from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { TextInput, View } from "react-native";

export const SearchInput = ({ search, setSearch }: { search: string; setSearch: (value: string) => void }) => {
  const { t } = useTranslation();
  return (
    <View
      style={{
        height: 42,
        borderRadius: 22,
        paddingHorizontal: 16,
        backgroundColor: getColor("foreground", "dim"),
        flexDirection: "row",
        alignItems: "center",
      }}
    >
      <Search size={16} color={getColor("foreground", "half")} style={{ position: "absolute", left: 16 }} />
      <TextInput
        style={[FontStyles.body, { paddingVertical: 12, paddingLeft: 24 }]}
        placeholder={t("uiPrompts.findSwap")}
        value={search}
        onChangeText={setSearch}
      />
    </View>
  );
};
