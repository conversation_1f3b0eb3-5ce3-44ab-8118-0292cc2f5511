import { UseSwapStationMiddlewareReturnType, useSwapStationMiddleware } from "@/hooks/useSwapStationMiddleware";

import { createContext, useContext } from "react";

type EnhancedSwapStationContextType = UseSwapStationMiddlewareReturnType & {
  partnerId: string;
};

const EnhancedSwapStationContext = createContext<EnhancedSwapStationContextType | null>(null);

export const EnhancedSwapStationProvider = ({
  children,
  partnerId,
}: {
  children: React.ReactNode;
  partnerId: string;
}) => {
  const swapStationData = useSwapStationMiddleware(partnerId);

  return (
    <EnhancedSwapStationContext.Provider
      value={{
        ...swapStationData,
        partnerId,
      }}
    >
      {children}
    </EnhancedSwapStationContext.Provider>
  );
};

export const useEnhancedSwapStation = () => {
  const context = useContext(EnhancedSwapStationContext);
  if (!context) {
    throw new Error("useEnhancedSwapStation must be used within an EnhancedSwapStationProvider");
  }
  return context;
};
