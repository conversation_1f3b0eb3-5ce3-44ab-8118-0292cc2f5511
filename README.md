# ext-consumer-swap-fe
Repository for external consumer facing battery swapping frontend.

React Native app with Expo.
Package name: com.aerovy.consumer_swap_fe

## Development

### Prerequisites

- Node.js (v20) + npm (10)

### Development

Follow the directions [here](https://docs.expo.dev/get-started/set-up-your-environment/?mode=development-build&buildEnv=local) to set up your local development environment.

```
$ cd ext-consumer-swap-fe
$ npm install
$ npx expo run:android
```

### Production Builds (Android) 

To build a release AAB, run the following: https://reactnative.dev/docs/signed-apk-android

In GitHub, the upload key and variables are stored as repository secrets / variables as:

* `UPLOAD_STORE_FILE`
* `UPLOAD_KEY_ALIAS`
* `UPLOAD_STORE_PASSWORD`
* `UPLOAD_KEY_PASSWORD`

Mapbox tokens are stored as:
* `MAPBOX_DOWNLOADS_TOKEN`
* `EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN`

And Android versioning can be set with
* `VERSION_CODE`
* `VERSION_NAME`

but to build locally, you need to set it in `ext-consumer-swap-fe/android/gradle.properties` or in `.env`. Then you can run:

```bash
$ cd ext-consumer-swap-fe
$ npm install
$ npx react-native build-android --mode=release # or dotenv -- npx react-native build-android --mode=release
# find the AAB in android/app/build/outputs/bundle/release/app-release.aab
```

### Convert keystore to/from base64

To convert the keystore to base64 text file, use the following
```bash
$ base64 -i ext-consumer-swap-fe-upload-key.keystore -o ext-consumer-swap-fe-upload-key.keystore.base64
```

To convert back:
```bash
$ base64 -d -i ext-consumer-swap-fe-upload-key.keystore.base64 -o ext-consumer-swap-fe-upload-key.keystore
```

### Prebuilding

You can prebuild only android by specifying
```bash
$ npx expo prebuild --platform android [--clean]
```

### Sideloading `.aab` onto device

To sideload an aab onto the device, use bundletool.

```bash
bundletool build-apks --bundle=android/app/build/outputs/bundle/release/app-release.aab --output=app-release.apks --ks android/app/ext-consumer-swap-fe-upload-key.keystore --ks-key-alias ext-consumer-swap-fe-upload-key
bundletool install-apks --apks=app-release.apks
```

### Setup git hooks
1. make sure you are in the root directory `ext-consumer-swap-fe`
2. run `bash ext-consumer-swap-fe/setup-pre-commit.sh` for pre-commit and `bash ext-consumer-swap-fe/setup-post-commit.sh`
3. For each `git commit` command, it will check and run prettier before actual commit happens

#### Troubleshooting
If error happens, do:
1. make sure to have the latest git version. [How to update git here.](https://stackoverflow.com/a/48953680/5767110)
2. update git hooks path by running this command `git config --local core.hooksPath .git/hooks` in root directory. [Reference.](https://stackoverflow.com/a/75449816/5767110)