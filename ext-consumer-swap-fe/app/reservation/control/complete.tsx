import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { FontStyles } from "@/constants/FontStyles";
import { useApiFieldContext } from "@/contexts/apiFieldContext";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import { languageBasedOnActionMap } from "@/locales/map";
import { ControlMiddleware } from "@/middleware/controlMiddleware";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";
import { Check } from "lucide-react-native";

import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const styles = StyleSheet.create({
  safeArea: { flex: 1, width: "100%", backgroundColor: getColor("background") },

  container: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "flex-start",
    paddingHorizontal: 16,
    gap: 32,
    paddingBottom: 32,
  },
});

export default function CompleteScreen() {
  const { transactionId, action } = useLocalSearchParams();
  const { reservation, token, partnerId } = useApiFieldContext();
  const showReservationActions = transactionId && reservation;

  const { refresh, stations } = useEnhancedSwapStation();
  const { t, i18n } = useTranslation();

  useEffect(() => {
    console.log("CompleteScreen/useEffect: reservation", reservation);
    if (!reservation) return;
    ControlMiddleware.updateReservationStatus(
      reservation.transactionId,
      "COMPLETED",
      token!,
      partnerId,
      reservation.placeType,
      reservation.placeId,
      reservation.thingId,
    );
    const swapStation = stations.find((station) => station.thingId === reservation.swapStationId);
    if (swapStation) {
      ControlMiddleware.forceRefresh(swapStation, token!).then(() => {
        // TODO: this is a hack to wait for the swap station to refresh, we should have the middleware handle this
        // refresh the swap stations after 2 seconds
        setTimeout(() => {
          refresh();
        }, 2000);
      });
    }
  }, [reservation]);

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <CloseButton
          style={{
            width: "100%",
            flexDirection: "row-reverse",
            justifyContent: "flex-start",
          }}
        />

        <View
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            gap: 32,
          }}
        >
          {/* icon */}
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              height: 96,
              width: 96,
              borderRadius: 16,
              backgroundColor: getColor("primary", "dim"),
            }}
          >
            <Check color={getColor("primary")} size={48} />
          </View>
          <View style={{ gap: 16, paddingHorizontal: 16 }}>
            <Text style={[FontStyles.title, { color: getColor("foreground"), textAlign: "center" }]}>
              {t("uiPrompts.complete", {
                action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
              })}
            </Text>
            <Text style={[FontStyles.body, { color: getColor("foreground", "half"), textAlign: "center" }]}>
              {t("uiPrompts.completeThanks", {
                action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
              })}
            </Text>
          </View>
        </View>
        <Button
          text={t("uiPrompts.bookAgain")}
          color={ButtonVariant.Color.PRIMARY}
          size={ButtonVariant.Size.LARGE}
          flex={true}
          onPress={() => router.replace("/")}
        />
        {showReservationActions && (
          <Text style={FontStyles.footnote}>
            {t("uiPrompts.reservationId")}: {reservation.transactionId}
          </Text>
        )}
      </View>
    </SafeAreaView>
  );
}
