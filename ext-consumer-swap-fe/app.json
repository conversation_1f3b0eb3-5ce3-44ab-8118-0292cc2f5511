{"expo": {"name": "ext-consumer-swap-fe", "slug": "ext-consumer-swap-fe", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.aerovy.id.ekomoto"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["react-native-auth0", {"domain": "aerovy-ext-dev.eu.auth0.com"}], ["@rnmapbox/maps", {"RNMapboxMapsDownloadToken": "", "RNMapboxMapsVersion": "11.7.1"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], "expo-font"], "experiments": {"typedRoutes": true}}}