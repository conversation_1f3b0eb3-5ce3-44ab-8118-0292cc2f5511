import { CloseButton } from "@/components/CloseButton";
import { FontStyles } from "@/constants/FontStyles";
import { transactions } from "@/static/transactions";
import { getColor } from "@/utils/style";
import { useLocalSearchParams } from "expo-router";
import { ArrowLeftRight, Clock, X } from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const styles = StyleSheet.create({
  safeArea: { flex: 1, width: "100%", backgroundColor: getColor("background") },

  container: {
    flex: 1,
    flexDirection: "column",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    paddingHorizontal: 16,
    gap: 32,
    paddingBottom: 32,
  },
});

const Components = () => {
  const { id } = useLocalSearchParams();
  const transaction = transactions.find((transaction) => transaction.id === id);

  const Header = () => {
    const icon = () => {
      if (transaction?.status === "Completed") return <ArrowLeftRight color={getColor("primary")} size={24} />;
      if (transaction?.status === "Cancelled") return <X color={getColor("foreground")} size={24} />;
      return <Clock color={getColor("foreground")} size={24} />;
    };

    const backgroundColor = () => {
      if (transaction?.status === "Completed") return "rgba(28, 160, 31, 0.05)";
      return "rgba(17, 39, 18, 0.05)";
    };

    return (
      <View style={{ gap: 32, flex: 1, width: "100%" }}>
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 48,
            width: 48,
            borderRadius: 16,
            backgroundColor: backgroundColor(),
          }}
        >
          {icon()}
        </View>
        <View style={{ gap: 4, flex: 1 }}>
          <Text style={FontStyles.heading1}>BATTERY SWAP</Text>
          <Text style={FontStyles.body}>{transaction?.location}</Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Text style={FontStyles.body}>{transaction?.date.toLocaleString()}</Text>
            <Text style={FontStyles.body}>Rp{transaction?.amount.toLocaleString()}</Text>
          </View>
        </View>
      </View>
    );
  };

  const Receipt = () => {
    const { t } = useTranslation();
    return (
      <View style={{ flex: 1 }}>
        <View style={{ gap: 14, flex: 1 }}>
          <Text style={FontStyles.heading2}>{t("uiPrompts.receipt")}</Text>

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.body}>{t("uiPrompts.swapFee")}</Text>
            <Text style={FontStyles.body}>{transaction?.amount.toLocaleString()}</Text>
          </View>

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.body}>{t("uiPrompts.tax")}</Text>
            <Text style={FontStyles.body}>{transaction?.amount.toLocaleString()}</Text>
          </View>

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.bodyEmphasized}>{t("uiPrompts.total")}</Text>
            <Text style={FontStyles.bodyEmphasized}>{transaction?.amount.toLocaleString()}</Text>
          </View>

          <View
            style={{
              borderBottomColor: getColor("foreground"),
              borderBottomWidth: 1,
              opacity: 0.2,
            }}
          />

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.body}>{t("uiPrompts.paidBy", { type: "OVO" })}</Text>
            <Text style={FontStyles.body}>Rp{transaction?.amount.toLocaleString()}</Text>
          </View>
        </View>
      </View>
    );
  };

  const ReservationTimeline = () => {
    const { t } = useTranslation();
    return (
      <View style={{ flex: 1 }}>
        <View style={{ gap: 14, flex: 1 }}>
          <Text style={FontStyles.heading2}>{t("uiPrompts.reservationTimeline")}</Text>

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.body}>{t("uiPrompts.swapBooked")}</Text>
            <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>
              {transaction?.date.toLocaleString()}
            </Text>
          </View>

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.body}>{t("uiPrompts.reservationExtended")}</Text>
            <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>
              {transaction?.date.toLocaleString()}
            </Text>
          </View>

          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={FontStyles.body}>{t("uiPrompts.swapCompleted")}</Text>
            <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>
              {transaction?.date.toLocaleString()}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const Footer = () => {
    const { t } = useTranslation();
    return (
      <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
        {t("uiPrompts.reservationId")}: {transaction?.id}
      </Text>
    );
  };

  return {
    Header,
    Receipt,
    ReservationTimeline,
    Footer,
  };
};

export default function ReceiptDetail() {
  const { Header, Receipt, ReservationTimeline, Footer } = Components();
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView>
        <View style={styles.container}>
          <CloseButton
            style={{
              width: "100%",
              flexDirection: "row-reverse",
              justifyContent: "flex-start",
            }}
          />

          <View style={{ gap: 36, flex: 1 }}>
            <Header />
            <Receipt />
            <ReservationTimeline />
            <Footer />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
