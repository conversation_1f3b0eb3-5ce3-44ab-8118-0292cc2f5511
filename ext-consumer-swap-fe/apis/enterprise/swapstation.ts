const API_URL = "https://spectra.api.dev.aerovy.com";

export type GenericAttributes = {
  [key: string]: string;
};

export type SwapStation = {
  thingId: string;
  thingName: string;
  thingType: string;
  thingDescription: string;
  address: string | null;
  model: string;
  thingManufacturerId: string;
  isSimulated: boolean;
  longitude: number;
  latitude: number;
  altitude: number;
  integrationId: string | null;
  partnerId: string;
  siteId: string;
  placeType: string;
  placeId: string;
  levelId: string;
  attributes: GenericAttributes;
  primaryGroup: string;
  createdAt: string;
  createdBy: string;
};

export type SwapStationBoxState = {
  stationBoxId: string;
  stationBoxState: "NoBattery" | "Charging" | "FullCharge" | "Error" | "UnknownBatteryInside" | "NotCharging";
  stationBoxIsCharging: boolean;
  stationBoxDoorState: "Open" | "Closed";
  batteryManufacturerId: string | null; // null if empty
};

export type SwapStationDetail = {
  stationState: "INIT" | "IDLE" | "EXCHANGING" | "RETURNING" | "RENTING" | "ERROR" | "LOCKED";
  stationBoxStates: SwapStationBoxState[];
  // batteriesAvailable: number; // TODO: these are unstable
  // fullBatteriesAvailable: number; // TODO: these are unstable
  batteryManufacturerIds: string[];
};

export type Battery = {
  thingId: string;
  thingManufacturerId: string;
  soc: number;
  soh: number;
  batteryState: "Moving" | "Idle" | "StorageMode" | "Sleep";
};

export type BatteryDetail = {
  batteryState: Battery;
  boxState: SwapStationBoxState;
};

export const getSwapStations = async (partnerId: string, primaryGroup?: string): Promise<SwapStation[]> => {
  const queryParams = new URLSearchParams();
  queryParams.set("partnerId", partnerId);
  if (primaryGroup) {
    queryParams.set("primaryGroup", primaryGroup);
  }
  const response = await fetch(`${API_URL}/swap-stations/data?${queryParams.toString()}`);
  return response.json();
};

export const getSwapStationDetail = async (
  partnerId: string,
  placeId: string,
  swapStationId: string,
): Promise<SwapStationDetail> => {
  const queryParams = new URLSearchParams();
  queryParams.set("partnerId", partnerId);
  queryParams.set("placeId", placeId);

  const response = await fetch(`${API_URL}/swap-stations/data/${swapStationId}?${queryParams.toString()}`);
  return response.json();
};

export const getSwapStationBatteries = async (
  partnerId: string,
  placeId: string,
  swapStationId: string,
): Promise<Battery[]> => {
  const queryParams = new URLSearchParams();
  queryParams.set("partnerId", partnerId);
  queryParams.set("placeId", placeId);

  const response = await fetch(`${API_URL}/swap-stations/data/${swapStationId}/batteries?${queryParams.toString()}`);
  return response.json();
};

export const getBatteryDetail = async (
  partnerId: string,
  placeId: string,
  swapStationId: string,
  batteryManufacturerId: string,
): Promise<BatteryDetail> => {
  const queryParams = new URLSearchParams();
  queryParams.set("partnerId", partnerId);
  queryParams.set("placeId", placeId);

  const response = await fetch(
    `${API_URL}/swap-stations/data/${swapStationId}/battery/${batteryManufacturerId}?${queryParams.toString()}`,
  );
  return response.json();
};

export const forceRefresh = async (swapStation: SwapStation, accessToken: string): Promise<void> => {
  const response = await fetch(`${API_URL}/swap-stations/data/refresh`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      swapStationPartnerId: swapStation.partnerId,
      swapStationThingId: swapStation.thingId,
      swapStationManufacturerId: swapStation.thingManufacturerId,
      swapStationPlaceId: swapStation.placeId,
    }),
  });
  if (!response.ok) {
    throw new Error(`Failed to force refresh: ${response.statusText}`);
  }
};
