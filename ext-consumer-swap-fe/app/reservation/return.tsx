import { DirectionsRoute, fetchDirections } from "@/apis/mapbox";
import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { DirectionsMap } from "@/components/DirectionsMap";
import { NavigateButton } from "@/components/NavigateButton";
import { SnappableBottomSheet } from "@/components/SnappableBottomSheet";
import { FontStyles } from "@/constants/FontStyles";
import { useApiFieldContext } from "@/contexts/apiFieldContext";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { EnhancedSwapStation } from "@/hooks/useSwapStationMiddleware";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";

import { useEffect } from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useWindowDimensions } from "react-native";
import { Text, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// A return-only

const ReturnView = ({ station }: { station: EnhancedSwapStation }) => {
  const { setSwapStation } = useApiFieldContext();
  const { t } = useTranslation();
  // TODO: reuse the same drawer component between stationid, new reservation, and reservation detail instead of duplicating the code
  return (
    <View style={{ flex: 1, padding: 16, gap: 32 }}>
      <CloseButton position="absolute" />

      {/* Reservation Details */}
      <View style={{ flexDirection: "column", gap: 16 }}>
        {/* Station Info */}
        <View style={{ flexDirection: "column", gap: 4 }}>
          <Text style={[FontStyles.heading1, { color: getColor("foreground") }]}>{t("uiPrompts.returnBattery")}</Text>
          <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>{station.thingName}</Text>
        </View>
        {/* Reservation Location */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            gap: 16,
          }}
        >
          <View style={{ flexDirection: "column", gap: 0, flexShrink: 1 }}>
            <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
              {t("uiPrompts.reservationLocation")}
            </Text>
            <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{station.address}</Text>
          </View>

          <View>
            <NavigateButton latitude={station.latitude} longitude={station.longitude} />
          </View>
        </View>
        {/* Reservation Directions */}
        <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {t("uiPrompts.directions")}
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{t("uiPrompts.pleaseClickArrive")}</Text>
        </View>

        {/* CTA */}

        <Button
          text={t("uiPrompts.imHere")}
          onPress={() => {
            setSwapStation(station);
            router.replace(`/reservation/control/start?stationId=${station.thingId}&action=return`);
          }}
          size={ButtonVariant.Size.LARGE}
          color={ButtonVariant.Color.PRIMARY}
        />
      </View>
    </View>
  );
};

export default function ReturnPage() {
  const { stationId } = useLocalSearchParams();

  const { stations } = useEnhancedSwapStation();
  const station = stations.find((station) => station.thingId === stationId);

  const { location } = useUserLocationContext();

  const [directions, setDirections] = useState<DirectionsRoute | null>(null);

  useEffect(() => {
    if (!location || !station) return;
    const fetchDirectionsAsync = async () => {
      const directions = await fetchDirections(location, stationLocation);
      setDirections(directions);
    };
    fetchDirectionsAsync();
  }, [station, location]);

  const insets = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  const initialBottomSheetHeight = (height - insets.top - insets.bottom) / 2;

  if (!station) {
    return null;
  }

  const stationLocation = {
    latitude: station.latitude,
    longitude: station.longitude,
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <DirectionsMap
          from={location}
          to={stationLocation}
          directions={directions}
          insets={{
            top: insets.top,
            bottom: initialBottomSheetHeight + 16,
            left: insets.left + 16,
            right: insets.right + 16,
          }}
        />
        <SnappableBottomSheet initialHeight={initialBottomSheetHeight}>
          <ReturnView station={station} />
        </SnappableBottomSheet>
      </View>
    </GestureHandlerRootView>
  );
}
