import { useRouter } from "expo-router";
import { X } from "lucide-react-native";

import { FlexStyle, StyleProp, View, ViewStyle } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";

export const CloseButton = ({
  top = 0,
  right = 0,
  position = "relative",
  style = {},
}: {
  top?: number;
  right?: number;
  position?: FlexStyle["position"];
  style?: StyleProp<ViewStyle>;
}) => {
  const router = useRouter();
  return (
    <View style={{ position, right, top, zIndex: 10, ...(style as ViewStyle) }}>
      <TouchableOpacity
        style={{
          height: 40,
          width: 40,
          margin: 16,
          backgroundColor: "#EEEEEC",
          borderRadius: 20,
          justifyContent: "center",
          alignItems: "center",
        }}
        onPressIn={() => {
          router.dismiss();
        }}
      >
        <X size={24} color="#000000" />
      </TouchableOpacity>
    </View>
  );
};
