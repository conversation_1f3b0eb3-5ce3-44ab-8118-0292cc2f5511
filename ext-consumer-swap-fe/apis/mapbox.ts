import Mapbox from "@rnmapbox/maps";

export type DirectionsRoute = {
  geometry: GeoJSON.LineString;
  distance: number; // meters
  duration: number; // seconds
};

export const fetchDirections = async (
  from: { latitude: number; longitude: number },
  to: { latitude: number; longitude: number },
  profile: "cycling" | "driving" | "walking" = "driving",
): Promise<DirectionsRoute> => {
  const apiUrl = `https://api.mapbox.com/directions/v5/mapbox/${profile}`;
  const params = new URLSearchParams();
  params.set("alternatives", "false");
  params.set("geometries", "geojson");
  params.set("overview", "full");
  params.set("steps", "false");
  params.set("access_token", process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN);

  const response = await fetch(
    `${apiUrl}/${from.longitude}%2C${from.latitude}%3B${to.longitude}%2C${to.latitude}?${params.toString()}`,
  );
  const json = await response.json();
  return json.routes[0] as DirectionsRoute;
};

export const getBoundingBox = (coordinates: GeoJSON.Position[]): [GeoJSON.Position, GeoJSON.Position] => {
  const minLat = Math.min(...coordinates.map((coord) => coord[1]));
  const maxLat = Math.max(...coordinates.map((coord) => coord[1]));
  const minLng = Math.min(...coordinates.map((coord) => coord[0]));
  const maxLng = Math.max(...coordinates.map((coord) => coord[0]));

  return [
    [minLng, minLat], // Southwest corner
    [maxLng, maxLat], // Northeast corner
  ] as [GeoJSON.Position, GeoJSON.Position];
};

export type ZoomAndCenter = {
  zoom: number;
  center: GeoJSON.Position;
};

export const getZoomAndCenter = (coordinates: GeoJSON.Position[]): ZoomAndCenter => {
  const minLat = Math.min(...coordinates.map((coord) => coord[1]));
  const maxLat = Math.max(...coordinates.map((coord) => coord[1]));
  const minLng = Math.min(...coordinates.map((coord) => coord[0]));
  const maxLng = Math.max(...coordinates.map((coord) => coord[0]));

  const center = {
    latitude: (minLat + maxLat) / 2,
    longitude: (minLng + maxLng) / 2,
  };

  const latDiff = maxLat - minLat;
  const lngDiff = maxLng - minLng;
  const maxDiff = Math.max(latDiff, lngDiff);

  const zoom = Math.log2(360 / maxDiff) - 0.25;

  return { zoom, center: [center.longitude, center.latitude] };
};
