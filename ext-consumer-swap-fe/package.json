{"name": "ext-consumer-swap-fe", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo-google-fonts/plus-jakarta-sans": "^0.2.3", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/native": "^6.0.2", "@rnmapbox/maps": "^10.1.33", "expo": "~51.0.28", "expo-constants": "~16.0.2", "expo-dev-client": "~4.0.28", "expo-font": "~12.0.10", "expo-linking": "~6.3.1", "expo-location": "^17.0.1", "expo-router": "~3.5.23", "expo-splash-screen": "~0.27.6", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-web-browser": "~13.0.3", "i18next": "^23.16.5", "lucide-react-native": "^0.454.0", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^15.1.1", "react-native": "0.74.5", "react-native-auth0": "^3.2.1", "react-native-gesture-handler": "~2.16.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "^15.8.0", "react-native-web": "~0.19.10"}, "devDependencies": {"@babel/core": "^7.20.0", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/jest": "^29.5.12", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "eslint": "^8.57.0", "eslint-config-expo": "~7.1.2", "jest": "^29.2.1", "jest-expo": "~51.0.3", "prettier": "3.4.2", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}