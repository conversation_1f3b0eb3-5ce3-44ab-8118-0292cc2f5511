import { getColor } from "@/utils/style";

import { View } from "react-native";

export const CurrentLocationView = () => {
  return (
    <View
      style={{
        borderWidth: 2,
        borderColor: getColor("background"),
        width: 24,
        height: 24,
        borderRadius: 12,
        backgroundColor: getColor("primary"),
        shadowColor: "#000000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.25,
        shadowRadius: 8,
        elevation: 5,
      }}
    />
  );
};
