#!/bin/bash

# Define the target directory and post-commit hook file path
TARGET_DIR=$pwd
HOOKS_DIR="$TARGET_DIR.git/hooks"
POST_COMMIT_FILE="$HOOKS_DIR/post-commit"

# Ensure the hooks directory exists
if [ ! -d "$HOOKS_DIR" ]; then
    echo "Error: Hooks directory does not exist at $HOOKS_DIR"
    exit 1
fi

# Write the content of the post-commit script
cat << 'EOF' > "$POST_COMMIT_FILE"
#!/bin/bash
git update-index -g
echo "Changes committed to git!"
EOF

# Make the post-commit file executable
chmod +x "$POST_COMMIT_FILE"

echo "Post-commit hook created successfully at $POST_COMMIT_FILE"
