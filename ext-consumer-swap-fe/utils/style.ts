import { ColorsMap, opacityHexCode } from "@/constants/Colors";
import { UseThemeColorType } from "@/hooks/useThemeColor";

export function getColor(
  type: UseThemeColorType["type"] = "primary",
  opacity: UseThemeColorType["opacity"] = "full",
  props: UseThemeColorType["props"] = {},
) {
  const theme = "light"; // TODO: make darkmode
  const hexCode = opacityHexCode[opacity];

  return `${ColorsMap[theme][type]}${hexCode}`;
}
