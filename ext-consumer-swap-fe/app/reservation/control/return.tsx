import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { FontStyles } from "@/constants/FontStyles";
import { useApiFieldContext } from "@/contexts/apiFieldContext";
import { useDevOverride } from "@/contexts/useDevOverride";
import { languageBasedOnActionMap } from "@/locales/map";
import { ControlMiddleware } from "@/middleware/controlMiddleware";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";
import { Video, X } from "lucide-react-native";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { ControlExtraActions, DoorText, GetHelpButton } from "./shared";

const styles = StyleSheet.create({
  safeArea: { flex: 1, width: "100%", backgroundColor: getColor("background") },

  container: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "flex-start",
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 16,
  },
});

export default function ReturnScreen() {
  const { transactionId, action } = useLocalSearchParams();
  const { reservation, swapStation, customerId, token } = useApiFieldContext();

  const { override } = useDevOverride();

  const [doorId, setDoorId] = useState<string | undefined>(undefined);
  const [statusText, setStatusText] = useState<string | undefined>(undefined);

  const { t, i18n } = useTranslation();
  // TODO: remove swap, this screen only return
  const callToAction =
    action == "swap"
      ? t("uiPrompts.continueToInstall")
      : t("uiPrompts.complete", {
          action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
        });

  const handleContinue = async () => {
    if (action == "swap") {
      // go to install
      router.replace(`/reservation/control/install?stationId=${swapStation?.thingId}`);
    } else {
      router.replace(`/reservation/control/complete?transactionId=${transactionId}&action=${action}`);
    }
  };

  const attemptToReturn = () => {
    if (!swapStation) return;
    ControlMiddleware.fullReturnAction(swapStation, customerId!, token!, setStatusText, setDoorId, override);
  };

  const handleCancel = () => {
    if (!reservation || !swapStation) return;
    ControlMiddleware.cancelReservation(
      reservation.transactionId,
      token!,
      swapStation.partnerId,
      swapStation.placeType,
      swapStation.placeId,
      swapStation.thingId,
    ).then(() => {
      router.replace(
        `/reservation/${reservation.transactionId}?placeId=${swapStation.placeId}&placeType=${swapStation.placeType}&thingId=${swapStation.thingId}&action=return`,
      );
    });
  };

  useEffect(() => {
    attemptToReturn();
  }, [swapStation]);

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <CloseButton
          style={{
            width: "100%",
            flexDirection: "row-reverse",
            justifyContent: "flex-start",
          }}
        />

        {/* video */}
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            borderRadius: 24,
            backgroundColor: getColor("foreground", "dim"),
            gap: 8,
            width: "100%",
          }}
        >
          <Text style={[FontStyles.caption, { color: getColor("foreground") }]}>Video or animation here</Text>
          <Video size={48} color={getColor("foreground")} />
        </View>

        <GetHelpButton />

        <View style={{ gap: 8 }}>
          <Text style={[FontStyles.heading1, { color: getColor("foreground") }]}>
            {t("uiPrompts.returnUsedBattery")}
          </Text>
          <DoorText doorId={doorId} statusText={statusText} />
          <Text style={[FontStyles.body, { color: getColor("foreground", "half"), textAlign: "center" }]}>
            {t("uiPrompts.returnUsedBatteryDesc")} {doorId}
          </Text>
        </View>

        <ControlExtraActions retryAction={attemptToReturn} cancelAction={handleCancel} />

        <Button
          text={callToAction}
          size={ButtonVariant.Size.LARGE}
          color={ButtonVariant.Color.PRIMARY}
          onPress={handleContinue}
          flex={true}
        />
      </View>
    </SafeAreaView>
  );
}
