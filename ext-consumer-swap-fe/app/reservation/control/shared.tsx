import { FontStyles } from "@/constants/FontStyles";
import { getColor } from "@/utils/style";
import { useRouter } from "expo-router";
import { CircleHelp, CircleX, RotateCcw, X } from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { FlexStyle, Pressable, StyleProp, Text, TouchableOpacity, View, ViewStyle } from "react-native";

export const GetHelpButton = () => {
  // TODO: implement
  const { t } = useTranslation();
  return (
    <View style={{ width: "100%", alignItems: "center", justifyContent: "center" }}>
      <TouchableOpacity onPress={() => {}}>
        <View style={{ flexDirection: "row", alignItems: "center", gap: 4 }}>
          <CircleHelp size={16} color={getColor("primary")} />
          <Text
            style={[
              FontStyles.heading3,
              {
                color: getColor("primary"),
                textDecorationLine: "underline",
              },
            ]}
          >
            {t("uiPrompts.getHelp")}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export const ControlExtraActions = ({
  cancelAction,
  retryAction,
}: {
  cancelAction?: () => void | undefined;
  retryAction?: () => void | undefined;
}) => {
  const { t } = useTranslation();
  return (
    <View
      style={{
        gap: 16,
        width: "100%",
        flexDirection: "row",
        justifyContent: "center",
      }}
    >
      {retryAction && (
        <Pressable onPress={retryAction}>
          <RotateCcw size={16} color={getColor("primary")} style={{ alignSelf: "center" }} />
          <Text style={[FontStyles.bodyEmphasized, { color: getColor("primary") }]}>{t("uiPrompts.tryAgain")}</Text>
        </Pressable>
      )}

      {cancelAction && (
        <Pressable onPress={cancelAction}>
          <CircleX size={16} color={getColor("destructive")} style={{ alignSelf: "center" }} />
          <Text style={[FontStyles.bodyEmphasized, { color: getColor("destructive") }]}>{t("uiPrompts.cancel")}</Text>
        </Pressable>
      )}
    </View>
  );
};

export const DoorText = ({ doorId, statusText }: { doorId?: string | undefined; statusText?: string | undefined }) => {
  const { t } = useTranslation();
  if (!doorId) {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          paddingVertical: 18,
          paddingHorizontal: 24,
          borderRadius: 16,
          backgroundColor: getColor("foreground", "dim"),
        }}
      >
        <Text style={[FontStyles.bodyEmphasized, { color: getColor("foreground") }]}>
          {statusText || t("uiPrompts.connectingToStation")}
        </Text>
      </View>
    );
  }

  return (
    <View
      style={{
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: 18,
        paddingHorizontal: 24,
        borderRadius: 16,
        backgroundColor: getColor("primary", "dim"),
      }}
    >
      <Text style={[FontStyles.title, { color: getColor("primary") }]}>
        {t("uiPrompts.door")} {doorId}
      </Text>
    </View>
  );
};
