import { UseUserLocationType, useUserLocation } from "@/hooks/useUserLocation";

import { ReactNode, createContext, useContext } from "react";

const UserLocationContext = createContext<UseUserLocationType | undefined>(undefined);

export const UserLocationProvider = ({ children }: { children: ReactNode }) => {
  const locationData = useUserLocation();

  return <UserLocationContext.Provider value={locationData}>{children}</UserLocationContext.Provider>;
};

export const useUserLocationContext = (): UseUserLocationType => {
  const context = useContext(UserLocationContext);
  if (!context) {
    throw new Error("useUserLocationContext must be used within a UserLocationProvider");
  }
  return context;
};
