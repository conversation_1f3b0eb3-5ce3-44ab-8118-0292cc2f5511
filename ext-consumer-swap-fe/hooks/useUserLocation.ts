import * as Location from "expo-location";

import { useEffect, useState } from "react";

export type LocationType = {
  latitude: number;
  longitude: number;
};

export type UseUserLocationType = {
  location: LocationType | null;
  error: string | null;
  getLocation: () => Promise<void>;
};

export const useUserLocation = (): UseUserLocationType => {
  const [location, setLocation] = useState<LocationType | null>(null);
  const [error, setError] = useState<string | null>(null);

  const getLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== "granted") {
        setError("Permission to access location was denied");
        return;
      }

      const position = await Location.getCurrentPositionAsync({
        accuracy: Location.LocationAccuracy.BestForNavigation,
      });

      setLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
      });
    } catch (error) {
      setError("There was an error getting your location");
    }
  };

  useEffect(() => {
    getLocation();
  }, []);

  return { location, error, getLocation };
};
