import { fetchDirections } from "@/apis/mapbox";
import { DirectionsRoute } from "@/apis/mapbox";
import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { DirectionsMap } from "@/components/DirectionsMap";
import { NavigateButton } from "@/components/NavigateButton";
import { SnappableBottomSheet } from "@/components/SnappableBottomSheet";
import { FontStyles } from "@/constants/FontStyles";
import { useApiFieldContext } from "@/contexts/apiFieldContext";
import { useDevOverride } from "@/contexts/useDevOverride";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { EnhancedSwapStation } from "@/hooks/useSwapStationMiddleware";
import { languageBasedOnActionMap } from "@/locales/map";
import { ControlMiddleware } from "@/middleware/controlMiddleware";
import { ErrorHandlingMiddleware } from "@/middleware/errorHandlingMiddleware";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, Text, TextInput, View, useWindowDimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const DEFAULT_RESERVATION_DURATION = 1 * 60 * 60 * 1000; // 1 hour

const BATTERY_ID_BASE = "BT207203012HBDJ2408";

const NewReservationView = ({ station, action }: { station: EnhancedSwapStation; action: "rent" | "swap" }) => {
  const [endDate, _] = useState<Date>(new Date(new Date().getTime() + DEFAULT_RESERVATION_DURATION));

  const { setOverride } = useDevOverride();

  const { partnerId, customerId, token } = useApiFieldContext();
  const [loading, setLoading] = useState(false);

  const [expectedBatteryToSwap, setExpectedBatteryToSwap] = useState<string | undefined>(undefined);
  const [overrideLastFive, setOverrideLastFive] = useState<string | undefined>(undefined);

  const { t, i18n } = useTranslation();

  useEffect(() => {
    if (action === "swap") {
      ControlMiddleware.getCurrentBatteryManufacturerId(partnerId, customerId!, token!).then(
        (batteryManufacturerId) => {
          setExpectedBatteryToSwap(batteryManufacturerId);
          setOverrideLastFive(batteryManufacturerId?.slice(-5));
        },
      );
    }
  }, [action]);

  const createNewReservation = async (station: EnhancedSwapStation, actionType: "rent" | "swap") => {
    setLoading(true);

    // TODO probably should check if undefined
    if (actionType === "rent") {
      const reservation = await ControlMiddleware.createRentReservationAction(partnerId, station, token!).catch(
        (error) => {
          console.error("NewReservationView/createNewReservation: error", error);
          ErrorHandlingMiddleware.showPopupMessage(error.message);
        },
      );
      setLoading(false);
      return reservation;
    } else {
      const reservation = await ControlMiddleware.createSwapReservationAction(customerId!, partnerId, station, token!, {
        batteryManufacturerId: BATTERY_ID_BASE + overrideLastFive,
      }).catch((error) => {
        console.error("NewReservationView/createNewReservation: error", error);
        ErrorHandlingMiddleware.showPopupMessage(error.message);
      });
      setLoading(false);
      return reservation;
    }
  };

  // TODO: reuse the same drawer component between stationid, new reservation, and reservation detail instead of duplicating the code
  return (
    <View style={{ flex: 1, padding: 16, gap: 32 }}>
      <CloseButton position="absolute" />

      {/* Reservation Details */}
      <View style={{ flexDirection: "column", gap: 16 }}>
        {/* Station Info */}
        <View style={{ flexDirection: "column", gap: 4 }}>
          <Text style={[FontStyles.heading1, { color: getColor("foreground") }]}>
            {t("uiPrompts.newReservationAction", {
              action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
            })}
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>{station.thingName}</Text>
        </View>
        {/* Reservation Location */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            gap: 16,
          }}
        >
          <View style={{ flexDirection: "column", gap: 0, flexShrink: 1 }}>
            <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
              {t("uiPrompts.reservationLocation")}
            </Text>
            <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{station.address}</Text>
          </View>

          <View>
            <NavigateButton latitude={station.latitude} longitude={station.longitude} />
          </View>
        </View>
        {/* Reservation End Time */}
        <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
            {t("uiPrompts.reservationEndTime")}
          </Text>
          <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{endDate.toLocaleString()}</Text>
        </View>
        {/* Reservation Price */}
        {/* <View style={{ flexDirection: "column", gap: 0 }}>
          <Text style={[FontStyles.caption, { color: getColor('foreground', 'half') }]}>
            Reservation Price
          </Text>
          <Text style={[FontStyles.body, { color: getColor('foreground') }]}>
            Rp{reservation.price.toLocaleString()}
          </Text>
        </View> */}

        {action === "swap" && (
          <>
            <View style={{ flexDirection: "column", gap: 0 }}>
              <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
                {t("uiPrompts.expectedBatteryToSwap")}
              </Text>
              <Text style={[FontStyles.body, { color: getColor("foreground") }]}>{expectedBatteryToSwap}</Text>
            </View>
            <View style={{ flexDirection: "column", gap: 0 }}>
              <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>
                {t("uiPrompts.overrideBatteryToSwap")}
              </Text>
              <View style={{ flexDirection: "row", gap: 8 }}>
                <Text style={[FontStyles.body, { color: getColor("foreground", "half") }]}>{BATTERY_ID_BASE}</Text>
                <TextInput placeholder="XXXXX" value={overrideLastFive || ""} onChangeText={setOverrideLastFive} />
              </View>
              {overrideLastFive?.length != 5 && (
                <Text style={[FontStyles.caption, { color: getColor("foreground", "half") }]}>Must have length=5</Text>
              )}
            </View>
          </>
        )}

        {/* CTA */}
        {!loading && (
          <Button
            text={t("uiPrompts.confirmReservation")}
            onPress={() => {
              setOverride({
                batteryManufacturerId: BATTERY_ID_BASE + overrideLastFive,
              });
              createNewReservation(station, action).then((reservation) => {
                router.replace(
                  `/reservation/${reservation.transactionId}?placeId=${station.placeId}&placeType=${station.placeType}&thingId=${station.thingId}&action=${action}`,
                );
              });
            }}
            size={ButtonVariant.Size.LARGE}
            color={ButtonVariant.Color.PRIMARY}
          />
        )}
        {loading && <ActivityIndicator />}
      </View>
    </View>
  );
};

export default function NewReservationScreen() {
  // TODO: get station info dynamically
  const { stationId, type } = useLocalSearchParams();

  const { stations } = useEnhancedSwapStation();
  const station = stations.find((station) => station.thingId === stationId);

  const { location } = useUserLocationContext();

  const [directions, setDirections] = useState<DirectionsRoute | null>(null);

  useEffect(() => {
    if (!location || !station) return;
    const fetchDirectionsAsync = async () => {
      const directions = await fetchDirections(location, stationLocation);
      setDirections(directions);
    };
    fetchDirectionsAsync();
  }, [station, location]);

  const insets = useSafeAreaInsets();
  const { height } = useWindowDimensions();

  const initialBottomSheetHeight = (height - insets.top - insets.bottom) / 2;

  if (!station) {
    return null;
  }

  const stationLocation = {
    latitude: station.latitude,
    longitude: station.longitude,
  };

  return (
    <View style={{ flex: 1 }}>
      <DirectionsMap
        from={location}
        to={stationLocation}
        directions={directions}
        insets={{
          top: insets.top,
          bottom: initialBottomSheetHeight + 16,
          left: insets.left + 16,
          right: insets.right + 16,
        }}
      />
      <SnappableBottomSheet initialHeight={initialBottomSheetHeight}>
        <NewReservationView station={station} action={type} />
      </SnappableBottomSheet>
    </View>
  );
}
