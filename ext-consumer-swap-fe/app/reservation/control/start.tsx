import { Reservation } from "@/apis/enterprise/reservations";
import { Button, ButtonVariant } from "@/components/Button";
import { CloseButton } from "@/components/CloseButton";
import { FontStyles } from "@/constants/FontStyles";
import { useApiFieldContext } from "@/contexts/apiFieldContext";
import { useUserLocation } from "@/hooks/useUserLocation";
import { languageBasedOnActionMap } from "@/locales/map";
import { ControlMiddleware } from "@/middleware/controlMiddleware";
import { ErrorHandlingMiddleware } from "@/middleware/errorHandlingMiddleware";
import { calculateDistance } from "@/utils/distance";
import { getColor } from "@/utils/style";
import { router, useLocalSearchParams } from "expo-router";
import { MapPinned } from "lucide-react-native";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const styles = StyleSheet.create({
  safeArea: { flex: 1, width: "100%", backgroundColor: getColor("background") },

  container: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 16,
    gap: 32,
    paddingBottom: 32,
  },
});

export const CancelButton = ({ reservation, action }: { reservation: Reservation; action: string }) => {
  const { placeType, placeId, thingId } = reservation;
  const { token, partnerId } = useApiFieldContext();

  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleCancelReservation = () => {
    setLoading(true);
    ControlMiddleware.cancelReservation(reservation.transactionId, token, partnerId, placeType, placeId, thingId)
      .then(() => {
        router.replace(
          `/reservation/${reservation.transactionId}?placeId=${placeId}&placeType=${placeType}&thingId=${thingId}&action=${action}`,
        );
      })
      .catch((error) => {
        console.error("cancel reservation: error", error);
        ErrorHandlingMiddleware.showPopupMessage(error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  if (loading) {
    return <ActivityIndicator />;
  }

  return (
    <TouchableOpacity style={{ width: "100%" }} onPress={handleCancelReservation}>
      <Text
        style={[
          FontStyles.heading3,
          {
            color: getColor("destructive"),
            textDecorationLine: "underline",
            textAlign: "center",
          },
        ]}
      >
        {t("uiPrompts.cancelReservation")}
      </Text>
    </TouchableOpacity>
  );
};

export default function StartScreen() {
  // WARNING: must set stationId or transactionId in the url params
  // or else this page wont work. stationID is for returns, transactionID is for swaps/rent

  const { stationId, transactionId, action } = useLocalSearchParams();
  const { reservation, swapStation } = useApiFieldContext();
  const showReservationActions = transactionId && reservation; // TODO: this isnt super robust, so fix this later
  const { t, i18n } = useTranslation();

  const { location } = useUserLocation();
  // calc distance between location and swapStation longitude and latitude
  const distance =
    location && swapStation && location.latitude && location.longitude && swapStation.longitude && swapStation.latitude
      ? calculateDistance(location.latitude, location.longitude, swapStation.latitude, swapStation.longitude)
      : undefined;

  const withinRange = distance && distance < 300; // TODO: figure out a good distance

  useEffect(() => {
    console.log("distance", distance);
  }, [distance]);

  const handleStart = () => {
    if (action === "return") {
      router.replace(`/reservation/control/return?stationId=${stationId}&action=return`);
    } else if (action === "swap") {
      router.replace(`/reservation/control/swap?stationId=${stationId}`);
    } else {
      router.replace(`/reservation/control/install?stationId=${stationId}`);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <CloseButton
          style={{
            width: "100%",
            flexDirection: "row-reverse",
            justifyContent: "flex-start",
          }}
        />

        <View
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            gap: 32,
          }}
        >
          {/* icon */}
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              height: 96,
              width: 96,
              borderRadius: 16,
              backgroundColor: getColor("primary", "dim"),
            }}
          >
            <MapPinned color={getColor("primary")} size={48} />
          </View>
          <View style={{ gap: 16, paddingHorizontal: 16 }}>
            <Text style={[FontStyles.title, { color: getColor("foreground"), textAlign: "center" }]}>
              {t("uiPrompts.arrivedAtStation")}
            </Text>
            <Text style={[FontStyles.body, { color: getColor("foreground", "half"), textAlign: "center" }]}>
              {t("uiPrompts.arrivedAtStationDesc", {
                action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
              })}
            </Text>
          </View>
        </View>
        <View
          style={{
            width: "100%",
            flexDirection: "column",
            gap: 8,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Button
            text={t("uiPrompts.start", {
              action: languageBasedOnActionMap[i18n.language][`${action}` ?? "rent"],
            })}
            color={ButtonVariant.Color.PRIMARY}
            size={ButtonVariant.Size.LARGE}
            flex={true}
            onPress={handleStart}
            disabled={!withinRange}
          />
          {!withinRange && (
            <Text style={[FontStyles.caption, { color: getColor("destructive") }]}>{t("uiPrompts.outOfRange")}</Text>
          )}
        </View>
        {showReservationActions && <CancelButton reservation={reservation} action={action} />}
        {showReservationActions && (
          <Text style={FontStyles.footnote}>
            {t("uiPrompts.reservationId")}: {reservation.transactionId}
          </Text>
        )}
      </View>
    </SafeAreaView>
  );
}
