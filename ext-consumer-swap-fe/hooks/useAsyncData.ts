import { useEffect, useState } from "react";

export type UseAsyncDataReturnType<T> = [T | null, boolean, string | null, () => Promise<void>];

export const useAsyncData = <T>(
  asyncFunction: () => Promise<T>,
  loadOnInit: boolean = true,
): UseAsyncDataReturnType<T> => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null); // Clear previous errors
    try {
      const result = await asyncFunction();
      setData(result);
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (loadOnInit) {
      fetchData();
    }
  }, [loadOnInit]);

  return [data, loading, error, fetchData];
};
