import { Map as MapComponent } from "@/components/Map";
import { MapErrorBoundary } from "@/components/MapErrorBoundary";
import { SearchableStationList } from "@/components/SearchableStationList";
import { useEnhancedSwapStation } from "@/contexts/useEnhancedSwapStationContext";
import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { EnhancedSwapStation } from "@/hooks/useSwapStationMiddleware";
import { getColor } from "@/utils/style";
import { MarkerView } from "@rnmapbox/maps";
import { useRouter } from "expo-router";
import { UserRound } from "lucide-react-native";

import { ActivityIndicator, Image, Text, TouchableOpacity, View } from "react-native";
import { Pressable } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { FontStyles } from "@/constants/FontStyles";

const MapStationMarker = ({ station }: { station: EnhancedSwapStation }) => {
  const router = useRouter();
  return (
    <TouchableOpacity
      onPress={() => {
        router.push(`/station/${station.thingId}`);
      }}
      style={{
        margin: 8,
      }}
    >
      <View
        style={{
          backgroundColor: getColor("primary"),
          padding: 4,
          borderRadius: 8,
          elevation: 5,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.15,
          shadowRadius: 4,
        }}
      >
        <Image source={require("@/assets/images/favicon.png")} style={{ height: 24, width: 24, borderRadius: 4 }} />
      </View>
    </TouchableOpacity>
  );
};

const StationMarkers = () => {
  const { stations, loading, error } = useEnhancedSwapStation();

  // Don't render markers if there's an error or still loading
  if (loading || error || !stations.length) {
    return null;
  }

  return (
    <>
      {stations.map((station) => (
        <MarkerView
          key={station.thingId}
          id={station.thingId}
          coordinate={[station.longitude, station.latitude]}
          allowOverlap={true}
          allowOverlapWithPuck={true}
        >
          <MapStationMarker station={station} />
        </MarkerView>
      ))}
    </>
  );
};

export const Map = () => {
  const { top, bottom } = useSafeAreaInsets();
  const { location, error } = useUserLocationContext();

  const router = useRouter();
  const heightFromBottom = 78 + bottom;

  // Show loading state only if there's no location and no error (still loading)
  const isLoadingLocation = !location && !error;

  return (
    <View style={{ flex: 1 }}>
      <MapErrorBoundary>
        <MapComponent insets={{ top, left: 16, bottom: heightFromBottom + 36, right: 16 }}>
          <StationMarkers />
        </MapComponent>
      </MapErrorBoundary>

        {/* Show loading overlay only briefly while location is being fetched */}
        {isLoadingLocation && (
          <View
            style={{
              position: "absolute",
              top: top + 60,
              left: 16,
              backgroundColor: getColor("background"),
              paddingHorizontal: 12,
              paddingVertical: 8,
              borderRadius: 8,
              flexDirection: "row",
              alignItems: "center",
              gap: 8,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.15,
              shadowRadius: 4,
              elevation: 5,
            }}
          >
            <ActivityIndicator size="small" color={getColor("primary")} />
            <Text style={[FontStyles.footnote, { color: getColor("foreground") }]}>
              Getting location...
            </Text>
          </View>
        )}

        <SearchableStationList initialHeight={heightFromBottom} distanceFromTop={72} />
        <Pressable
          style={{
            position: "absolute",
            top: top + 16,
            right: 16,
            backgroundColor: getColor("background"),
            width: 40,
            height: 40,
            borderRadius: 20,
            justifyContent: "center",
            alignItems: "center",
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.15,
            shadowRadius: 4,
            elevation: 5,
          }}
          onPress={() => {
            router.push("/profile");
          }}
        >
          <UserRound color={getColor("primary")} size={24} />
        </Pressable>
      </View>
    </View>
  );
};
