import React, { Component, ReactNode } from "react";
import { View, Text } from "react-native";
import { getColor } from "@/utils/style";
import { FontStyles } from "@/constants/FontStyles";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class MapErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Map Error Boundary caught an error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: getColor("background"),
            padding: 16,
          }}
        >
          <Text
            style={[
              FontStyles.bodyEmphasized,
              { color: getColor("destructive"), textAlign: "center", marginBottom: 8 }
            ]}
          >
            Map Error
          </Text>
          <Text
            style={[
              FontStyles.body,
              { color: getColor("foreground"), textAlign: "center" }
            ]}
          >
            Unable to load the map. Please try restarting the app.
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}
