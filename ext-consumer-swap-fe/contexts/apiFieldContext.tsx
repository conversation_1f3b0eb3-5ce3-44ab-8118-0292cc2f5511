// this just holds a bunch of fields that are used in multiple places
import { Reservation } from "@/apis/enterprise/reservations";
import { SwapStation } from "@/apis/enterprise/swapstation";

import { ReactNode, createContext, useContext, useEffect, useState } from "react";
import { useAuth0 } from "react-native-auth0";

export type ApiFieldContextType = {
  customerId: string | undefined;
  partnerId: string;
  token: string | undefined;

  // TODO: remove this later but this is the easiest way to share the reservation between screens
  reservation: Reservation | undefined;
  setReservation: (reservation: Reservation) => void;

  swapStation: SwapStation | undefined;
  setSwapStation: (swapStation: SwapStation) => void;
};

// TODO: remove these hardcoded values
// const partnerId = "00000000-ad9d-4025-81ab-e6083532bb7b";
const DEFAULT_PARTNER_ID = "5fe49f4b-ad65-4863-a221-f2664f06e335";
const DEFAULT_CUSTOMER_ID = "TESTCUSTOMER";

export const ApiFieldContext = createContext<ApiFieldContextType>({
  customerId: undefined,
  partnerId: DEFAULT_PARTNER_ID,
  token: undefined,
  reservation: undefined,
  setReservation: () => {},
  swapStation: undefined,
  setSwapStation: () => {},
});

export const useApiFieldContext = () => {
  return useContext(ApiFieldContext);
};

export const ApiFieldContextProvider = ({ children }: { children: ReactNode }) => {
  const [customerId, setCustomerId] = useState<string | undefined>(DEFAULT_CUSTOMER_ID);
  const [partnerId, setPartnerId] = useState<string>(DEFAULT_PARTNER_ID);
  const [token, setToken] = useState<string | undefined>(undefined);
  const [reservation, setReservation] = useState<Reservation | undefined>(undefined);
  const [swapStation, setSwapStation] = useState<SwapStation | undefined>(undefined);

  const { user, getCredentials } = useAuth0();

  useEffect(() => {
    if (!user) {
      return;
    }

    getCredentials().then((credentials) => {
      setToken(credentials?.accessToken);
      // TODO: set customerId here?
    });
  }, [user]);

  return (
    <ApiFieldContext.Provider
      value={{
        customerId,
        partnerId,
        token,
        reservation,
        setReservation,
        swapStation,
        setSwapStation,
      }}
    >
      {children}
    </ApiFieldContext.Provider>
  );
};
