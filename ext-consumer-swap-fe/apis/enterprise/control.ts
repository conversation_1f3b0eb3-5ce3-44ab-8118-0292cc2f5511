import { Reservation } from "./reservations";
import { Battery, BatteryDetail, SwapStation } from "./swapstation";

const API_URL = "https://spectra.api.dev.aerovy.com";

export type RawDeviceResponse = {
  // msgType: number;
  // devId: string;
  txnNo: string;
  transactionId: string;
  //   paramList: [
  //     {
  //       [key: string]: string;
  //     },
  //   ];
};

export type StartControlEventResponse = RawDeviceResponse;

export const rentBattery = async (
  swapStation: SwapStation,
  reservation: Reservation,
  accessToken: string,
): Promise<StartControlEventResponse> => {
  const body = {
    swapStationPartnerId: swapStation.partnerId,
    swapStationThingId: swapStation.thingId,
    swapStationManufacturerId: swapStation.thingManufacturerId,
    swapStationPlaceId: swapStation.placeId,
    swapStationDoorId: reservation.reservedBatteryDoorId, // battery.boxState.stationBoxId,
    batteryManufacturerId: reservation.reservedBatteryManufacturerId, // battery.batteryState.thingManufacturerId,
  };
  console.log("rentBattery", body);
  const response = await fetch(`${API_URL}/swap-stations/control/rent`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    throw new Error(`Failed to rent battery: ${response.statusText}`);
  }
  return response.json();
};

export const returnBattery = async (
  swapStation: SwapStation,
  stationBoxId: string,
  batteryManufacturerId: string, // use Battery type if we can get the details
  accessToken: string,
): Promise<StartControlEventResponse> => {
  const body = {
    swapStationPartnerId: swapStation.partnerId,
    swapStationThingId: swapStation.thingId,
    swapStationManufacturerId: swapStation.thingManufacturerId,
    swapStationPlaceId: swapStation.placeId,
    swapStationDoorId: stationBoxId,
    batteryManufacturerId: batteryManufacturerId,
  };
  console.log("returnBattery", body);
  const response = await fetch(`${API_URL}/swap-stations/control/return`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    throw new Error(`Failed to return battery: ${response.statusText}`);
  }
  return response.json();
};

export const swapBattery = async (
  swapStation: SwapStation,
  stationBoxId: string,
  newBatteryManufacturerId: string,
  newBatteryDoorId: string,
  oldBatteryManufacturerId: string,
  accessToken: string,
): Promise<StartControlEventResponse> => {
  const body = {
    swapStationPartnerId: swapStation.partnerId,
    swapStationThingId: swapStation.thingId,
    swapStationManufacturerId: swapStation.thingManufacturerId,
    swapStationPlaceId: swapStation.placeId,
    swapStationReturnDoorId: stationBoxId,
    emptyBatteryManufacturerId: oldBatteryManufacturerId,
    fullBatteryManufacturerId: newBatteryManufacturerId,
    fullBatteryDoorId: newBatteryDoorId,
  };
  console.log("swapBattery", body);
  const response = await fetch(`${API_URL}/swap-stations/control/swap`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    throw new Error(`Failed to swap battery: ${response.statusText}`);
  }
  return response.json();
};

export enum ControlEventStatus {
  // Core Progress States
  Pending = "Pending",
  InProgress = "InProgress",
  Completed = "Completed",

  // Error/Failure States
  Failed = "Failed",
  Timeout = "Timeout",
  InvalidRequest = "InvalidRequest",
  Rejected = "Rejected",
  Conflict = "Conflict",

  // Cancellation States
  Cancelled = "Cancelled",
  Interrupted = "Interrupted",

  // Expirations
  Expired = "Expired",
  Stale = "Stale",

  // Queued States
  Queued = "Queued",
  Deferred = "Deferred",

  // Success Variations
  PartiallyCompleted = "PartiallyCompleted",
  Confirmed = "Confirmed",
  Acknowledged = "Acknowledged",
  Accepted = "Accepted",

  // Retry or Recovery States
  Retrying = "Retrying",
  Recovering = "Recovering",

  // Device-Specific States
  AwaitingDeviceResponse = "AwaitingDeviceResponse",
  DeviceUnavailable = "DeviceUnavailable",
  DeviceError = "DeviceError",

  // Escalation States
  Escalated = "Escalated",
  ManualInterventionRequired = "ManualInterventionRequired",

  // Audit or Diagnostic States
  Logged = "Logged",
  Debugging = "Debugging",
}

export type SwapStationStatusEvent = {
  eventTime: string;
  status: ControlEventStatus;
  referenceId: string;
  metadata: {};
};

export type SwapStationStatus = {
  events: SwapStationStatusEvent[];
  controlType: string;
  partnerId: string;
  externalPartnerId: string;
  ownerId: string;
  transactionId: string;
  transactionType: string;
  transactionStatus: string;
  thingId: string;
  placeId: string;
  placeType: string;
  //   metrics: {};
  createdAt: string;
  createdBy: string;
};

export const getSwapStationStatus = async (
  swapStation: SwapStation,
  transactionId: string,
  accessToken: string,
): Promise<SwapStationStatus> => {
  const response = await fetch(`${API_URL}/swap-stations/control/status`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      swapStationPartnerId: swapStation.partnerId,
      swapStationThingId: swapStation.thingId,
      swapStationPlaceId: swapStation.placeId,
      transactionId,
    }),
  });
  if (!response.ok) {
    throw new Error(`Failed to get swap station status: ${response.statusText}`);
  }
  return response.json();
};

export const confirmControlEvent = async (
  controlName: "rent" | "return" | "swap",
  swapStation: SwapStation,
  transactionId: string,
  recordId: string,
  accessToken: string,
): Promise<RawDeviceResponse> => {
  const body = {
    transactionId,
    recordId,
    swapStationPartnerId: swapStation.partnerId,
    swapStationManufacturerId: swapStation.thingManufacturerId,
    swapStationThingId: swapStation.thingId,
    swapStationPlaceId: swapStation.placeId,
  };
  console.log("confirmControlEvent", controlName, body);
  const response = await fetch(`${API_URL}/swap-stations/control/${controlName}/confirm`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    throw new Error(`Failed to confirm control event: ${response.statusText}`);
  }
  return response.json();
};
