import { StyleSheet } from "react-native";

const Fonts = {
  InterBlack: "Inter_900Black",
  PlusJakartaSansRegular: "PlusJakartaSans_400Regular",
  PlusJakartaSansMedium: "PlusJakartaSans_500Medium",
  PlusJakartaSansSemiBold: "PlusJakartaSans_600SemiBold",
  PlusJakartaSansBold: "PlusJakartaSans_700Bold",
};

export const FontStyles = StyleSheet.create({
  title: {
    fontFamily: Fonts.InterBlack,
    fontSize: 32,
    textTransform: "uppercase",
    lineHeight: 32,
    letterSpacing: -1.6,
  },

  heading1: {
    fontFamily: Fonts.InterBlack,
    fontSize: 20,
    textTransform: "uppercase",
    lineHeight: 20,
    letterSpacing: -1,
  },

  heading2: {
    fontFamily: Fonts.InterBlack,
    fontSize: 14,
    textTransform: "uppercase",
    lineHeight: 20,
    letterSpacing: -0.7,
  },

  heading3: {
    fontFamily: Fonts.PlusJakartaSansMedium,
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: -0.28,
  },

  body: {
    fontFamily: Fonts.PlusJakartaSansRegular,
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: -0.28,
  },

  bodyEmphasized: {
    fontFamily: Fonts.PlusJakartaSansBold,
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: -0.28,
  },

  link: {
    fontFamily: Fonts.PlusJakartaSansSemiBold,
    fontSize: 14,
    textDecorationLine: "underline",
    lineHeight: 18,
    letterSpacing: -0.28,
  },

  caption: {
    fontFamily: Fonts.PlusJakartaSansMedium,
    fontSize: 12,
    lineHeight: 15,
    letterSpacing: -0.24,
  },

  footnote: {
    fontFamily: Fonts.PlusJakartaSansRegular,
    fontSize: 10,
    lineHeight: 12,
    letterSpacing: -0.2,
  },
});
