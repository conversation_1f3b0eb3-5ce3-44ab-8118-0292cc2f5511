import { FontStyles } from "@/constants/FontStyles";
import { getColor } from "@/utils/style";
import { Plus } from "lucide-react-native";
import { Minus } from "lucide-react-native";

import { createContext, useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { Text, TouchableOpacity, View } from "react-native";
import Svg, { Path } from "react-native-svg";

const INITIAL_CHARGE_LEVEL = 5;
const INITIAL_PASSENGERS = 1;
const MAX_PASSENGERS = 2;
const MIN_PASSENGERS = 1;
const INITIAL_MAX_DISTANCE = 70 * 1000; // 70 km

export type FindByChargeLevelContextType = {
  chargeLevel: number;
  passengers: number;
  maxDistance: number;
  setChargeLevel: (chargeLevel: number) => void;
  setPassengers: (passengers: number) => void;
};

export const FindByChargeLevelContext = createContext<FindByChargeLevelContextType>({
  chargeLevel: INITIAL_CHARGE_LEVEL,
  passengers: INITIAL_PASSENGERS,
  maxDistance: INITIAL_MAX_DISTANCE,
  setChargeLevel: (chargeLevel: number) => {},
  setPassengers: (passengers: number) => {},
});

export const useFindByChargeLevelContext = () => {
  return useContext(FindByChargeLevelContext);
};

export const FindByChargeLevelProvider = ({ children }: { children: React.ReactNode }) => {
  const [chargeLevel, setChargeLevelState] = useState(INITIAL_CHARGE_LEVEL);
  const [passengers, setPassengersState] = useState(INITIAL_PASSENGERS);
  const maxDistance = (INITIAL_MAX_DISTANCE / passengers) * (chargeLevel / 5);

  const setChargeLevel = (level: number) => {
    // Clamp the value between 1 and 5
    const clampedLevel = Math.min(Math.max(level, 1), 5);
    setChargeLevelState(clampedLevel);
  };

  const setPassengers = (num: number) => {
    const clampedNum = Math.min(Math.max(num, MIN_PASSENGERS), MAX_PASSENGERS);
    setPassengersState(clampedNum);
  };

  return (
    <FindByChargeLevelContext.Provider
      value={{
        chargeLevel,
        setChargeLevel,
        passengers,
        setPassengers,
        maxDistance,
      }}
    >
      {children}
    </FindByChargeLevelContext.Provider>
  );
};

export const FindByChargeLevelSelector = () => {
  const { t } = useTranslation();
  return (
    <View
      style={{
        paddingVertical: 16,
        paddingHorizontal: 12,
        gap: 12,
        backgroundColor: getColor("foreground", "dim"),
        borderRadius: 24,
        alignItems: "center",
      }}
    >
      <Text style={[FontStyles.bodyEmphasized, { color: getColor("foreground") }]}>{t("uiPrompts.findByCharge")}</Text>
      <Text
        style={[
          FontStyles.footnote,
          {
            textAlign: "center",
            color: getColor("foreground", "half"),
          },
        ]}
      >
        {t("uiPrompts.selectBatteryLevel")}
      </Text>
      <View style={{ flexDirection: "row", gap: 24, alignItems: "center" }}>
        <Slider />
        <PassengerSelector />
      </View>
    </View>
  );
};

const PassengerSelector = () => {
  const { passengers, setPassengers } = useFindByChargeLevelContext();
  const { t } = useTranslation();

  const canDecrement = passengers > 1;
  const canIncrement = passengers < 2;

  return (
    <View style={{ alignItems: "center", gap: 4 }}>
      <View style={{ flexDirection: "row", gap: 8, alignItems: "center" }}>
        <TouchableOpacity
          onPress={() => setPassengers(passengers - 1)}
          style={{
            padding: 4,
            borderRadius: 16,
            backgroundColor: getColor("foreground", "dim"),
            opacity: canDecrement ? 1 : 0.5,
          }}
          disabled={!canDecrement}
        >
          <Minus size={24} color={getColor("foreground")} />
        </TouchableOpacity>
        <Text style={FontStyles.body}>{passengers}</Text>
        <TouchableOpacity
          onPress={() => setPassengers(passengers + 1)}
          style={{
            padding: 4,
            borderRadius: 16,
            backgroundColor: getColor("foreground", "dim"),
            opacity: canIncrement ? 1 : 0.5,
          }}
          disabled={!canIncrement}
        >
          <Plus size={24} color={getColor("foreground")} />
        </TouchableOpacity>
      </View>
      <Text style={[FontStyles.footnote, { textAlign: "center" }]}>{t("uiPrompts.riders")}</Text>
    </View>
  );
};

const Slider = () => {
  const color = getColor("primary");
  const uncoloredColor = getColor("foreground", "dim");

  const { chargeLevel, setChargeLevel } = useFindByChargeLevelContext();
  const { t } = useTranslation();

  return (
    <View style={{ alignItems: "center", gap: 4 }}>
      <Svg width={164} height={35} fill="none">
        <Path
          d="M6.495 6h23.5l-6.5 23h-17V6z"
          fill={chargeLevel >= 1 ? color : uncoloredColor}
          onPress={() => setChargeLevel(1)}
        />

        <Path
          d="M33.995 6h27.5l-6.5 23h-27.5l6.5-23z"
          fill={chargeLevel >= 2 ? color : uncoloredColor}
          onPress={() => setChargeLevel(2)}
        />

        <Path
          d="M65.495 6h29.5l-6.5 23h-29.5l6.5-23z"
          fill={chargeLevel >= 3 ? color : uncoloredColor}
          onPress={() => setChargeLevel(3)}
        />

        <Path
          d="M98.995 6h27l-6.5 23h-27l6.5-23z"
          fill={chargeLevel >= 4 ? color : uncoloredColor}
          onPress={() => setChargeLevel(4)}
        />

        <Path
          d="M129.995 6h18v23h-24.5l6.5-23z"
          fill={chargeLevel >= 5 ? color : uncoloredColor}
          onPress={() => setChargeLevel(5)}
        />

        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M.495 0H154v10.5h9.505v14H154V35H.495V23h2v10H152V2H2.495v10h-2V0zm161.01 12.5H154v10h7.505v-10z"
          fill={getColor("foreground")}
        />
      </Svg>
      <Text style={[FontStyles.footnote, { textAlign: "center" }]}>{t("uiPrompts.batteryLevel")}</Text>
    </View>
  );
};
