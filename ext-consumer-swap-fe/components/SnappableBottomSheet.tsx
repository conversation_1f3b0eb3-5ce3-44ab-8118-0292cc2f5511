import { useEffect, useState } from "react";
import { Dimensions, Keyboard, View } from "react-native";
import { Gesture, GestureDetector, ScrollView } from "react-native-gesture-handler";
import Animated, { interpolate, runOnJS, useAnimatedStyle, useSharedValue, withSpring } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export const SnappableBottomSheet = ({
  initialHeight,
  additionalSnapPoints = [],
  children,
  distanceFromTop = 0,
}: {
  initialHeight: number;
  additionalSnapPoints?: number[];
  distanceFromTop?: number;
  children: React.ReactNode;
}) => {
  const { top, bottom } = useSafeAreaInsets();
  const { height } = Dimensions.get("window");

  const translateY = useSharedValue(-initialHeight);
  const context = useSharedValue({ y: 0 });

  const [isScrollEnabled, setIsScrollEnabled] = useState(false);

  const MAX_HEIGHT = height - distanceFromTop - bottom;
  const MAX_TRANSLATE_Y = -height + distanceFromTop + top;
  const MIN_TRANSLATE_Y = -initialHeight;

  const updateScrollEnabled = (offset: number) => {
    "worklet";
    runOnJS(setIsScrollEnabled)(offset <= MAX_TRANSLATE_Y);
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener("keyboardDidShow", () => {
      translateY.value = withSpring(MAX_TRANSLATE_Y, { damping: 50 });
    });
    return () => keyboardDidShowListener.remove();
  }, []);

  const panGesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = Math.max(Math.min(event.translationY + context.value.y, 0), MAX_TRANSLATE_Y);
      updateScrollEnabled(translateY.value);
    })
    .onEnd(() => {
      const snapPoints = [MIN_TRANSLATE_Y, ...additionalSnapPoints.map((point) => -point), MAX_TRANSLATE_Y];

      const closestSnapPoint = snapPoints.reduce((prev, curr) =>
        Math.abs(curr - translateY.value) < Math.abs(prev - translateY.value) ? curr : prev,
      );

      translateY.value = withSpring(closestSnapPoint, { damping: 50 });
      updateScrollEnabled(translateY.value);
    });

  const rBottomSheetStyle = useAnimatedStyle(() => {
    const borderRadius = interpolate(translateY.value, [MAX_TRANSLATE_Y + 50, MAX_TRANSLATE_Y], [32, 0], "clamp");

    return {
      transform: [{ translateY: translateY.value }],
      borderTopLeftRadius: borderRadius,
      borderTopRightRadius: borderRadius,
    };
  });
  return (
    <GestureDetector gesture={panGesture}>
      <Animated.View
        style={[
          {
            height: MAX_HEIGHT,
            width: "100%",
            position: "absolute",
            top: height,
            backgroundColor: "white",
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.15,
            shadowRadius: 4,
            elevation: 5,
          },
          rBottomSheetStyle,
        ]}
      >
        {/* Drag indicator */}
        <View
          style={{
            width: 80,
            height: 4,
            backgroundColor: "#DDD",
            borderRadius: 2,
            alignSelf: "center",
            marginVertical: 8,
          }}
        />
        {/* Content */}
        <View style={{ flex: 1, paddingBottom: bottom }}>
          <ScrollView style={{ flex: 1 }} contentContainerStyle={{ paddingBottom: 16 }} scrollEnabled={isScrollEnabled}>
            {children}
          </ScrollView>
        </View>
      </Animated.View>
    </GestureDetector>
  );
};
