import { FontStyles } from "@/constants/FontStyles";
import { getColor } from "@/utils/style";

import { Pressable, Text, View } from "react-native";

enum ButtonSizeVariant {
  SMALL = "small",
  MEDIUM = "medium",
  LARGE = "large",
}

enum ButtonColorVariant {
  PRIMARY = "primary",
  SECONDARY = "secondary",
  GHOST = "ghost",
  DESTRUCTIVE = "destructive",
}

export enum ButtonIconPosition {
  LEFT = "left",
  RIGHT = "right",
}

export const ButtonVariant = {
  Size: ButtonSizeVariant,
  Color: ButtonColorVariant,
  IconPosition: ButtonIconPosition,
};

const buttonSizeVariantPadding = {
  [ButtonSizeVariant.SMALL]: { paddingHorizontal: 12, paddingVertical: 6 },
  [ButtonSizeVariant.MEDIUM]: { paddingHorizontal: 16, paddingVertical: 10 },
  [ButtonSizeVariant.LARGE]: { paddingHorizontal: 18, paddingVertical: 12 },
};

const buttonColorVariantBackgroundColor = {
  [ButtonColorVariant.PRIMARY]: getColor("primary"),
  [ButtonColorVariant.SECONDARY]: getColor("primary", "dim"),
  [ButtonColorVariant.GHOST]: "transparent",
  [ButtonColorVariant.DESTRUCTIVE]: getColor("destructive", "dim"),
};

const buttonColorVariantTextColor = {
  [ButtonColorVariant.PRIMARY]: getColor("background"),
  [ButtonColorVariant.SECONDARY]: getColor("primary"),
  [ButtonColorVariant.GHOST]: getColor("primary"),
  [ButtonColorVariant.DESTRUCTIVE]: getColor("destructive"),
};

const buttonColorVariantBorderColor = {
  [ButtonColorVariant.PRIMARY]: "transparent",
  [ButtonColorVariant.SECONDARY]: "transparent",
  [ButtonColorVariant.GHOST]: getColor("primary"),
  [ButtonColorVariant.DESTRUCTIVE]: "transparent",
};

const buttonColorVariantBorderWidth = {
  [ButtonColorVariant.PRIMARY]: 0,
  [ButtonColorVariant.SECONDARY]: 0,
  [ButtonColorVariant.GHOST]: 2,
  [ButtonColorVariant.DESTRUCTIVE]: 0,
};

const buttonFontVariant = {
  [ButtonSizeVariant.SMALL]: FontStyles.caption,
  [ButtonSizeVariant.MEDIUM]: FontStyles.heading3,
  [ButtonSizeVariant.LARGE]: FontStyles.heading2,
};

export const Button = ({
  text,
  subText = undefined,
  onPress = () => {},
  size = ButtonSizeVariant.MEDIUM,
  color = ButtonColorVariant.SECONDARY,
  icon = undefined,
  iconPosition = ButtonIconPosition.LEFT,
  disabled = false,
  flex = false,
}: {
  text: string;
  subText?: string;
  onPress?: () => void;
  size?: ButtonSizeVariant;
  color?: ButtonColorVariant;
  icon?: React.ReactNode;
  iconPosition?: ButtonIconPosition;
  disabled?: boolean;
  flex?: boolean;
}) => {
  const textStyle = [
    buttonFontVariant[size],
    {
      color: buttonColorVariantTextColor[color],
    },
  ];

  const buttonStyle = {
    ...buttonSizeVariantPadding[size],
    backgroundColor: buttonColorVariantBackgroundColor[color],
    borderRadius: 12,
    borderWidth: buttonColorVariantBorderWidth[color],
    borderColor: buttonColorVariantBorderColor[color],
    opacity: disabled ? 0.5 : 1, // TODO: make this a color variable
    width: flex ? "100%" : undefined,
  };

  return (
    <Pressable onPress={disabled ? undefined : onPress} style={buttonStyle}>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          gap: 4,
        }}
      >
        {icon && iconPosition === ButtonIconPosition.LEFT && icon}
        <View style={{ alignItems: "center" }}>
          <Text style={textStyle}>{text.toString()}</Text>
          {subText && (
            <Text style={[FontStyles.footnote, { color: buttonColorVariantTextColor[color] }]}>
              {subText.toString()}
            </Text>
          )}
        </View>
        {icon && iconPosition === ButtonIconPosition.RIGHT && icon}
      </View>
    </Pressable>
  );
};
