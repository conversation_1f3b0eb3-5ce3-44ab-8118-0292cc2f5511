import { createContext, useContext, useState } from "react";

export type DevOverride = {
  batteryManufacturerId?: string | undefined;
};

const DevOverrideContext = createContext<{
  override: DevOverride;
  setOverride: (override: DevOverride) => void;
}>({
  override: {},
  setOverride: () => {},
});

export const DevOverrideProvider = ({ children }: { children: React.ReactNode }) => {
  const [override, setOverride] = useState<DevOverride>({});

  return <DevOverrideContext.Provider value={{ override, setOverride }}>{children}</DevOverrideContext.Provider>;
};

export const useDevOverride = () => useContext(DevOverrideContext);
