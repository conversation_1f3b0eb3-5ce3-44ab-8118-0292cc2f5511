export type Transaction = {
  type: "Battery Swap";
  id: string;
  amount: number;
  location: string;
  date: Date;
  status: "Completed" | "Pending" | "Cancelled";
};

export const transactions: Transaction[] = [
  {
    id: "585ad4a0-6d55-478a-b2b8-25b8c968698b",
    type: "Battery Swap",
    amount: 30_000,
    location: "Gedung Jalan Sudirman No. 45",
    date: new Date(2024, 10, 18, 9, 44, 0),
    status: "Completed",
  },
  {
    id: "9be1f883-b79f-43f8-85f1-b9093395c9e6",
    type: "Battery Swap",
    amount: 30_000,
    location: "Tower Jalan Thamrin No. 89",
    date: new Date(2024, 10, 14, 10, 20, 0),
    status: "Completed",
  },
  {
    id: "acb338ef-42bf-44cb-be49-83bdf562ebf6",
    type: "Battery Swap",
    amount: 30_000,
    location: "Gedung Jalan Sudirman No. 45",
    date: new Date(2024, 10, 12, 14, 13, 0),
    status: "Completed",
  },
  {
    id: "3c945fcd-2538-4f48-aa0e-1020da15f469",
    type: "Battery Swap",
    amount: 30_000,
    location: "Gedung Jalan Sudirman No. 45",
    date: new Date(2024, 10, 11, 13, 8, 0),
    status: "Cancelled",
  },
  {
    id: "f3876bf0-fe18-47f0-8046-0b50451d328b",
    type: "Battery Swap",
    amount: 30_000,
    location: "Tower Jalan Thamrin No. 89",
    date: new Date(2024, 10, 8, 11, 30, 0),
    status: "Completed",
  },
];
