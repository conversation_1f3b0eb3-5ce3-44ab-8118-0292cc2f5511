import { ApiFieldContextProvider } from "@/contexts/apiFieldContext";
import { DevOverrideProvider } from "@/contexts/useDevOverride";
import { EnhancedSwapStationProvider } from "@/contexts/useEnhancedSwapStationContext";
import { UserLocationProvider } from "@/contexts/useUserLocationContext";
import { Inter_900Black, useFonts as useInter } from "@expo-google-fonts/inter";
import {
  PlusJakartaSans_400Regular,
  PlusJakartaSans_500Medium,
  PlusJakartaSans_600SemiBold,
  PlusJakartaSans_700Bold,
  useFonts as usePlusJakartaSans,
} from "@expo-google-fonts/plus-jakarta-sans";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";

import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Auth0Provider } from "react-native-auth0";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import "./i18n";

SplashScreen.preventAutoHideAsync();

// TODO: dont hardcode partnerId
// const partnerId = "00000000-ad9d-4025-81ab-e6083532bb7b";
const partnerId = "5fe49f4b-ad65-4863-a221-f2664f06e335";

export default function RootLayout() {
  const [interLoaded] = useInter({
    Inter_900Black,
  });

  const [plusJakartaSansLoaded] = usePlusJakartaSans({
    PlusJakartaSans_400Regular,
    PlusJakartaSans_600SemiBold,
    PlusJakartaSans_500Medium,
    PlusJakartaSans_700Bold,
  });

  const fontsLoaded = interLoaded && plusJakartaSansLoaded;

  const { i18n } = useTranslation();

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  const getAndSetSelectedLanguage = async () => {
    try {
      const locale = await AsyncStorage.getItem("language");
      if (locale !== null) {
        // value previously stored
        i18n.changeLanguage(locale);
      }
    } catch (e) {
      // default on error
      i18n.changeLanguage("en");
      await AsyncStorage.setItem("language", "en");
    }
  };

  useEffect(() => {
    if (i18n) {
      getAndSetSelectedLanguage();
    }
  }, [i18n]);

  // TODO: probably come up with a better solution than using a key to force a re-render of the stack
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Auth0Provider domain={"aerovy-ext-dev.eu.auth0.com"} clientId={"p5y956mdmaKrwLfp12FtL9yA1OBsz2uz"}>
        <DevOverrideProvider>
          <UserLocationProvider>
            <ApiFieldContextProvider>
              <EnhancedSwapStationProvider partnerId={partnerId}>
                <Stack key={fontsLoaded ? "loaded" : "loading"}>
                  <Stack.Screen name="index" options={{ headerShown: false }} />
                  <Stack.Screen name="profile/index" options={{ headerShown: false }} />
                  <Stack.Screen name="profile/languages" options={{ headerShown: false }} />
                  <Stack.Screen name="station/[id]" options={{ headerShown: false }} />
                  <Stack.Screen name="receipt/[id]" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/[id]" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/new" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/return" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/control/start" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/control/return" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/control/swap" options={{ headerShown: false }} />
                  <Stack.Screen name="reservation/control/install" options={{ headerShown: false }} />
                  {/* <Stack.Screen
                  name="reservation/control/error"
                  options={{ headerShown: false }}
                /> */}
                  <Stack.Screen name="reservation/control/complete" options={{ headerShown: false }} />
                </Stack>
              </EnhancedSwapStationProvider>
            </ApiFieldContextProvider>
          </UserLocationProvider>
        </DevOverrideProvider>
      </Auth0Provider>
    </GestureHandlerRootView>
  );
}
