#!/bin/bash

# Define the target directory and pre-commit hook file path
TARGET_DIR=$pwd
HOOKS_DIR="$TARGET_DIR.git/hooks"
PRE_COMMIT_FILE="$HOOKS_DIR/pre-commit"

# Ensure the hooks directory exists
if [ ! -d "$HOOKS_DIR" ]; then
    echo "Error: Hooks directory does not exist at $HOOKS_DIR"
    exit 1
fi

# Write the content of the pre-commit script
cat << 'EOF' > "$PRE_COMMIT_FILE"
#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

echo "(1) Pick staged files.."
FILES=$(git diff --cached --name-only --diff-filter=ACMR | sed 's|^ext-consumer-swap-fe/||; s| |\\ |g')
[ -z "$FILES" ] && exit 0

echo "List of staged files:"
echo "$FILES"

echo "(2) Prettify all staged files.."
echo "Current working directory:"
pwd
echo "Change directory to source code.."
cd ext-consumer-swap-fe
pwd
# Prettify all selected files
echo "Running prettier write for files:"
if ! echo "$FILES" | xargs ./node_modules/.bin/prettier --write; then
    echo "Error: Prettier failed."
    echo "Current working directory:"
    pwd
    exit 1
else
    echo "Running prettier write success!"
fi

echo "(3) Add back the modified/prettified files to staging.."
# Add back the modified/prettified files to staging
if ! echo "$FILES" | xargs git add; then
    echo "Error: Failed to add files to staging."
    exit 1
else
    echo "Files added to staging.."
fi

echo "(4) Running actual git commit command:"

exit 0
EOF

# Make the pre-commit file executable
chmod +x "$PRE_COMMIT_FILE"

echo "Pre-commit hook created successfully at $PRE_COMMIT_FILE"
