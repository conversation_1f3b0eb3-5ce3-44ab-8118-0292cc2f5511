import { getColor } from "@/utils/style";
import { CornerUpRight } from "lucide-react-native";

import { useTranslation } from "react-i18next";
import { Linking, Platform } from "react-native";

import { Button, ButtonVariant } from "./Button";

export const NavigateButton = ({ latitude, longitude }: { latitude: number; longitude: number }) => {
  const url = Platform.select({
    ios: `maps://?daddr=${latitude},${longitude}`, // TODO: verify this works on iOS
    android: `google.navigation:q=${latitude},${longitude}`,
    default: `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}&travelmode=two-wheeler`,
  });
  const { t } = useTranslation();

  return (
    <Button
      text={t("uiPrompts.navigate")}
      onPress={() => {
        Linking.openURL(url);
      }}
      size={ButtonVariant.Size.MEDIUM}
      color={ButtonVariant.Color.SECONDARY}
      icon={<CornerUpRight size={16} color={getColor("primary")} />}
    />
  );
};
