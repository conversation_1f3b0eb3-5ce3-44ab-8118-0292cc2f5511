import { FontStyles } from "@/constants/FontStyles";
import { getColor } from "@/utils/style";

import { Pressable, Text } from "react-native";

export const FilterButton = ({
  text,
  value,
  onPress,
}: {
  text: string;
  value: boolean;
  onPress: (value: boolean) => void;
}) => {
  const selectedStyles = {
    backgroundColor: getColor("primary"),
    borderColor: getColor("primary"),
  };

  const deselectedStyles = {
    backgroundColor: "#FFF",
    borderColor: getColor("foreground", "half"),
  };

  return (
    <Pressable
      onPress={() => onPress(!value)}
      style={[
        {
          borderRadius: 6,
          paddingHorizontal: 12 - 2, // -2 to account for border width
          paddingVertical: 6,
          flexDirection: "row",
          alignItems: "center",
          borderWidth: 1,
        },
        value ? selectedStyles : deselectedStyles,
      ]}
    >
      <Text
        style={[
          FontStyles.caption,
          value ? { color: getColor("background") } : { color: getColor("foreground", "half") },
        ]}
      >
        {text}
      </Text>
    </Pressable>
  );
};
