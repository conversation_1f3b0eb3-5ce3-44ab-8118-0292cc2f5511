import { SwapStation, SwapStationDetail, getSwapStationDetail, getSwapStations } from "@/apis/enterprise";
import { useUserLocationContext } from "@/contexts/useUserLocationContext";
import { useAsyncData } from "@/hooks/useAsyncData";
import { calculateDistance } from "@/utils/distance";

import { useEffect, useState } from "react";

export type WithDistance = {
  distance: number;
};

export type EnhancedSwapStation = SwapStation & Partial<WithDistance> & Partial<SwapStationDetail>;

export type UseSwapStationMiddlewareReturnType = {
  stations: EnhancedSwapStation[];
  loading: boolean;
  error: string | null;

  refresh: () => void;
};

export const useSwapStationMiddleware = (partnerId: string): UseSwapStationMiddlewareReturnType => {
  const { location } = useUserLocationContext();
  const [stations, stationsLoading, stationsError, refreshStations] = useAsyncData(() => getSwapStations(partnerId));
  const [details, setDetails] = useState<{
    [thingId: string]: SwapStationDetail;
  }>({});
  const [distances, setDistances] = useState<{
    [thingId: string]: number;
  }>({});

  // fetch station details
  useEffect(() => {
    if (!stations) {
      return;
    }

    stations.forEach(async (station) => {
      const detail = await getSwapStationDetail(partnerId, station.placeId, station.thingId);

      setDetails((prev) => ({ ...prev, [station.thingId]: detail }));
    });
  }, [stations]);

  // calculate distance for each swap station
  useEffect(() => {
    if (!location || !stations) {
      return;
    }

    stations.forEach(async (station) => {
      const distance = calculateDistance(location.latitude, location.longitude, station.latitude, station.longitude);

      setDistances((prev) => ({ ...prev, [station.thingId]: distance }));
    });
  }, [location, stations]);

  const enhancedStations = stations?.map((station) => ({
    ...station,
    ...details[station.thingId],
    distance: distances[station.thingId],
  }));

  return {
    stations: enhancedStations || [],
    loading: stationsLoading,
    error: stationsError,
    refresh: refreshStations,
  };
};
